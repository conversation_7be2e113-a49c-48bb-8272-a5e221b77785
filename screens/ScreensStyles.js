// ScreensStyles.js - Centralized styling for the Shake & Match app
import { StyleSheet, Platform } from 'react-native';

// ===== THEME DEFINITIONS =====
const themes = {
  default: {
    colors: {
      primary: '#e83333',           // Main red color
      primaryDark: '#d12929',       // Darker red for pressed states
      primaryLight: '#ffe5e5',      // Light red background

      // Secondary Colors
      secondary: '#f8f9fa',         // Light gray
      accent: '#ff6b6b',           // Accent red

      // Text Colors
      textPrimary: '#2c3e50',      // Dark text
      textSecondary: '#7f8c8d',    // Medium gray text
      textLight: '#bdc3c7',        // Light gray text
      textWhite: '#ffffff',        // White text
      textError: '#ff3b30',        // Error red
      textSuccess: '#28a745',      // Success green

      // Background Colors
      bgPrimary: '#ffffff',        // White background
      bgSecondary: '#f8f8f8',      // Light gray background
      bgTertiary: '#e9ecef',       // Tertiary background
      bgError: '#fff5f5',          // Error background
      bgSuccess: '#f0fff4',        // Success background

      // Border Colors
      borderPrimary: '#dee2e6',    // Default border
      borderSecondary: '#e0e0e0',  // Secondary border
      borderError: '#ff3b30',      // Error border
      borderSuccess: '#28a745',    // Success border

      // Special Colors
      gold: '#FFD700',             // Gold for premium features
      blue: '#4e9af1',             // Blue accent
      blueDark: '#3a7bc8',         // Dark blue

      // Overlay Colors
      overlayDark: 'rgba(0, 0, 0, 0.5)',
      overlayLight: 'rgba(255, 255, 255, 0.9)',
      shadowColor: '#000000',
    },
  },

  dark: {
    colors: {
      primary: '#e83333',           // Main red color (kept for consistency)
      primaryDark: '#d12929',       // Darker red for pressed states
      primaryLight: '#2a1a1a',      // Dark red background

      // Secondary Colors
      secondary: '#2c2c2c',         // Dark gray
      accent: '#ff6b6b',           // Accent red

      // Text Colors
      textPrimary: '#ffffff',      // White text
      textSecondary: '#b0b0b0',    // Light gray text
      textLight: '#808080',        // Medium gray text
      textWhite: '#ffffff',        // White text
      textError: '#ff3b30',        // Error red
      textSuccess: '#28a745',      // Success green

      // Background Colors
      bgPrimary: '#1a1a1a',        // Dark background
      bgSecondary: '#2c2c2c',      // Darker gray background
      bgTertiary: '#3a3a3a',       // Tertiary dark background
      bgError: '#2a1a1a',          // Dark error background
      bgSuccess: '#1a2a1a',        // Dark success background

      // Border Colors
      borderPrimary: '#404040',    // Dark border
      borderSecondary: '#505050',  // Secondary dark border
      borderError: '#ff3b30',      // Error red
      borderSuccess: '#28a745',    // Success green

      // Special Colors
      gold: '#FFD700',             // Gold for premium features
      blue: '#4e9af1',             // Blue accent
      blueDark: '#3a7bc8',         // Dark blue

      // Overlay Colors
      overlayDark: 'rgba(0, 0, 0, 0.8)',
      overlayLight: 'rgba(0, 0, 0, 0.6)',
      shadowColor: '#000000',
    },
  },

  blueish: {
    colors: {
      primary: '#4e9af1',           // Main blue color
      primaryDark: '#3a7bc8',       // Darker blue for pressed states
      primaryLight: '#e5f2ff',      // Light blue background

      // Secondary Colors
      secondary: '#f8f9fa',         // Light gray
      accent: '#6bb6ff',           // Accent blue

      // Text Colors
      textPrimary: '#2c3e50',      // Dark text
      textSecondary: '#7f8c8d',    // Medium gray text
      textLight: '#bdc3c7',        // Light gray text
      textWhite: '#ffffff',        // White text
      textError: '#ff3b30',        // Error red
      textSuccess: '#28a745',      // Success green

      // Background Colors
      bgPrimary: '#ffffff',        // White background
      bgSecondary: '#f8f9fa',      // Light gray background
      bgTertiary: '#e9ecef',       // Tertiary background
      bgError: '#fff5f5',          // Error background
      bgSuccess: '#f0fff4',        // Success background

      // Border Colors
      borderPrimary: '#dee2e6',    // Default border
      borderSecondary: '#e0e0e0',  // Secondary border
      borderError: '#ff3b30',      // Error border
      borderSuccess: '#28a745',    // Success border

      // Special Colors
      gold: '#FFD700',             // Gold for premium features
      blue: '#4e9af1',             // Blue accent
      blueDark: '#3a7bc8',         // Dark blue

      // Overlay Colors
      overlayDark: 'rgba(0, 0, 0, 0.5)',
      overlayLight: 'rgba(255, 255, 255, 0.9)',
      shadowColor: '#000000',
    },
  },
};

// Current theme - will be updated by theme context
let currentTheme = 'default';

// Function to get current theme
export const getCurrentTheme = () => currentTheme;

// Function to set current theme
export const setCurrentTheme = (themeName) => {
  if (themes[themeName]) {
    currentTheme = themeName;
  }
};

// Function to get current theme colors
export const getThemeColors = () => themes[currentTheme].colors;

// Dynamic theme object that returns current theme colors
export const theme = {
  get colors() {
    return themes[currentTheme].colors;
  },

  // Typography
  typography: {
    // Font Sizes
    fontSize: {
      xs: 12,
      sm: 14,
      md: 16,
      lg: 18,
      xl: 20,
      xxl: 24,
      xxxl: 32,
    },

    // Font Weights
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },

    // Line Heights
    lineHeight: {
      tight: 1.2,
      normal: 1.4,
      relaxed: 1.6,
    },
  },

  // Spacing
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
    xxxl: 32,
    xxxxl: 40,
  },

  // Border Radius
  borderRadius: {
    sm: 6,
    md: 8,
    lg: 12,
    xl: 16,
    xxl: 20,
    round: 50,
  },

  // Shadows
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 6,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.2,
      shadowRadius: 12,
      elevation: 8,
    },
  },

  // Animation Durations
  animation: {
    fast: 200,
    normal: 300,
    slow: 500,
  },
};

// ===== COMMON STYLES =====
export const commonStyles = StyleSheet.create({
  // Containers
  container: {
    flex: 1,
    backgroundColor: theme.colors.bgPrimary,
  },

  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.primary,
  },

  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Text Styles
  textPrimary: {
    color: theme.colors.textPrimary,
    fontSize: theme.typography.fontSize.md,
  },

  textSecondary: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.fontSize.sm,
  },

  textError: {
    color: theme.colors.textError,
    fontSize: theme.typography.fontSize.sm,
  },

  // Button Styles
  primaryButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.xl,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
  },

  primaryButtonText: {
    color: theme.colors.textWhite,
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
  },

  secondaryButton: {
    borderWidth: 1,
    borderColor: theme.colors.primary,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.xl,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
  },

  secondaryButtonText: {
    color: theme.colors.primary,
    fontSize: theme.typography.fontSize.md,
    fontWeight: theme.typography.fontWeight.semibold,
  },

  // Form Styles
  formGroup: {
    marginBottom: theme.spacing.xl,
  },

  label: {
    fontSize: theme.typography.fontSize.md,
    fontWeight: theme.typography.fontWeight.medium,
    marginBottom: theme.spacing.sm,
    color: theme.colors.textPrimary,
  },

  input: {
    backgroundColor: theme.colors.bgSecondary,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
    borderColor: theme.colors.borderSecondary,
    padding: theme.spacing.md,
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textPrimary,
  },

  inputError: {
    borderColor: theme.colors.borderError,
  },

  // Card Styles
  card: {
    backgroundColor: theme.colors.bgPrimary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    ...theme.shadows.sm,
  },

  // Loading Styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },

  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.typography.fontSize.md,
    color: theme.colors.textSecondary,
  },
});

// ===== LOGIN SCREEN STYLES =====
export const loginStyles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.primary,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 25,
    paddingTop: Platform.OS === 'ios' ? 20 : 0,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
    zIndex: -2,
  },
  floatingShape: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 80,
    zIndex: -1,
  },
  floatingShape1: {
    width: 200,
    height: 200,
    top: 20,
    right: -100,
    transform: [{ rotate: '30deg' }],
  },
  floatingShape2: {
    width: 180,
    height: 180,
    top: '65%',
    left: -90,
    transform: [{ rotate: '15deg' }],
  },
  floatingShape3: {
    width: 120,
    height: 120,
    top: '20%',
    left: -60,
    transform: [{ rotate: '45deg' }],
  },
  floatingShape4: {
    width: 100,
    height: 100,
    top: '80%',
    right: -50,
    transform: [{ rotate: '60deg' }],
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 20,
  },
  appTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.textWhite,
    textAlign: 'center',
    marginBottom: 8,
  },
  appSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 30,
    alignItems: 'center',
    ...theme.shadows.lg,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  welcomeSubtext: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 30,
    textAlign: 'center',
  },
  socialButtonsContainer: {
    width: '100%',
    marginBottom: 20,
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.bgPrimary,
    borderWidth: 1,
    borderColor: theme.colors.borderPrimary,
    borderRadius: 12,
    paddingVertical: 15,
    paddingHorizontal: 20,
    marginBottom: 12,
    ...theme.shadows.sm,
  },
  socialButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginLeft: 12,
  },
  orDivider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
    width: '100%',
  },
  orLine: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.borderPrimary,
  },
  orText: {
    marginHorizontal: 15,
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  inputContainer: {
    width: '100%',
    marginBottom: 15,
  },
  input: {
    backgroundColor: theme.colors.bgSecondary,
    borderRadius: 12,
    paddingVertical: 15,
    paddingHorizontal: 20,
    fontSize: 16,
    borderWidth: 1,
    borderColor: theme.colors.borderSecondary,
  },
  loginButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 15,
    paddingHorizontal: 40,
    width: '100%',
    alignItems: 'center',
    marginTop: 10,
    ...theme.shadows.sm,
  },
  loginButtonText: {
    color: theme.colors.textWhite,
    fontSize: 18,
    fontWeight: '600',
  },
  switchModeContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  switchModeText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  switchModeButton: {
    marginTop: 5,
  },
  switchModeButtonText: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  errorText: {
    color: theme.colors.textError,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 10,
  },
  loadingContainer: {
    marginTop: 20,
  },
});

// ===== HOME SCREEN STYLES =====
export const homeStyles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#ffe0e0',
  },
  gradientBackground: {
    flex: 1,
  },
  container: {
    flex: 1,
    position: 'relative',
  },
  // Floating decorative shapes
  floatingShape: {
    position: 'absolute',
    zIndex: -1,
  },
  floatingShape1: {
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: 'rgba(220, 38, 38, 0.12)',
    top: 20,
    right: -90,
  },
  floatingShape2: {
    width: 150,
    height: 150,
    borderRadius: 20,
    backgroundColor: 'rgba(239, 68, 68, 0.10)',
    top: '45%',
    left: -75,
  },
  floatingShape3: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(248, 113, 113, 0.08)',
    top: '70%',
    right: -60,
  },
  floatingShape4: {
    width: 100,
    height: 100,
    borderRadius: 15,
    backgroundColor: 'rgba(252, 165, 165, 0.12)',
    top: '25%',
    left: -50,
  },
  floatingShape5: {
    width: 90,
    height: 90,
    borderRadius: 45,
    backgroundColor: 'rgba(254, 202, 202, 0.15)',
    top: '85%',
    left: '30%',
  },
  floatingShape6: {
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: 40,
    borderRightWidth: 40,
    borderBottomWidth: 70,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'rgba(127, 29, 29, 0.07)',
    top: '15%',
    right: 80,
  },
  floatingShape7: {
    width: 140,
    height: 140,
    borderRadius: 25,
    backgroundColor: 'rgba(254, 202, 202, 0.13)',
    bottom: 300,
    left: -70,
  },
  floatingShape8: {
    width: 90,
    height: 90,
    borderRadius: 45,
    backgroundColor: 'rgba(153, 27, 27, 0.06)',
    top: '60%',
    left: '70%',
  },
  floatingShape9: {
    width: 110,
    height: 110,
    borderRadius: 55,
    backgroundColor: 'rgba(239, 68, 68, 0.08)',
    top: '35%',
    right: 20,
  },
  floatingShape10: {
    width: 130,
    height: 130,
    borderRadius: 18,
    backgroundColor: 'rgba(220, 38, 38, 0.09)',
    bottom: 200,
    left: 50,
  },
  floatingShape11: {
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: 35,
    borderRightWidth: 35,
    borderBottomWidth: 60,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'rgba(185, 28, 28, 0.06)',
    top: '55%',
    left: 20,
  },
  floatingShape12: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(248, 113, 113, 0.07)',
    bottom: 400,
    right: 60,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  welcomeSection: {
    padding: 25,
    alignItems: 'center',
    zIndex: 2,
  },
  welcomeText: {
    fontSize: 26,
    fontWeight: '700',
    marginBottom: 12,
    color: theme.colors.textPrimary,
    textAlign: 'center',
  },
  instructionText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    lineHeight: 22,
    textAlign: 'center',
  },
  shakeSection: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
    zIndex: 2,
  },
  shakeContainer: {
    alignItems: 'center',
  },
  shakeButton: {
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.lg,
    borderWidth: 4,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  shakeButtonActive: {
    backgroundColor: theme.colors.primaryDark,
    transform: [{ scale: 0.95 }],
  },
  shakeButtonText: {
    color: theme.colors.textWhite,
    fontSize: 18,
    fontWeight: '700',
    marginTop: 8,
  },
  shakeIcon: {
    marginBottom: 8,
  },
  shakeStatus: {
    marginTop: 15,
    alignItems: 'center',
  },
  shakeStatusText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  matchesSection: {
    flex: 1,
    paddingHorizontal: 20,
    zIndex: 2,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 15,
    color: theme.colors.textPrimary,
    marginTop: 5,
    marginLeft: 5,
  },
  matchesList: {
    flex: 1,
  },
  noMatches: {
    alignItems: 'center',
    padding: 40,
    borderRadius: 15,
    backgroundColor: '#f8fafc',
    marginTop: 10,
    ...theme.shadows.sm,
  },
  noMatchesIcon: {
    width: 48,
    height: 48,
    marginBottom: 15,
    opacity: 0.5,
    color: '#ccc',
  },
  noMatchesText: {
    fontSize: 16,
    lineHeight: 22,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  matchCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#f8fafc',
    borderRadius: 15,
    marginBottom: 10,
    ...theme.shadows.sm,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  matchCardPressed: {
    backgroundColor: '#f0f4f8',
    borderColor: theme.colors.primary,
  },
  matchAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 15,
    backgroundColor: theme.colors.bgSecondary,
  },
  defaultAvatar: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
  },
  matchInfo: {
    flex: 1,
  },
  matchName: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 4,
  },
  matchDetails: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  matchTime: {
    fontSize: 12,
    color: theme.colors.textLight,
  },
  matchActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  deleteButton: {
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
  },
  unreadIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.primary,
    marginLeft: 10,
  },

  // Additional HomeScreen styles that were missing
  swipeActionsContainer: {
    flexDirection: 'row',
    width: 170,
    height: 'auto',
  },
  actionContainer: {
    height: 'auto',
  },
  deleteAction: {
    backgroundColor: '#ff5a52',
    justifyContent: 'center',
    alignItems: 'center',
    width: 85,
    padding: 15,
    height: '90%',
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    shadowColor: '#ff5a52',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 3,
  },
  unmatchAction: {
    backgroundColor: '#5b7bf0',
    justifyContent: 'center',
    alignItems: 'center',
    width: 85,
    padding: 15,
    height: '90%',
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    shadowColor: '#5b7bf0',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 3,
  },
  blockAction: {
    backgroundColor: '#ff4757',
    justifyContent: 'center',
    alignItems: 'center',
    width: 85,
    padding: 15,
    height: '90%',
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    shadowColor: '#ff4757',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 3,
  },
  actionText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 12,
    marginTop: 4,
  },

  // Profile icon styles
  profileIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
  },
  profileIconImage: {
    width: '100%',
    height: '100%',
    borderRadius: 14,
  },

  // Header styling
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerButtonLeft: {
    padding: 8,
  },
  headerButtonRight: {
    padding: 8,
  },

  // Profile section styling
  profileSection: {
    padding: 25,
    alignItems: 'center',
  },
  locationIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  locationText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
  premiumLocationText: {
    color: '#B8860B',
    fontWeight: '600',
  },
  premiumLocationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFD700',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 8,
  },
  premiumLocationBadgeText: {
    fontSize: 10,
    color: '#000',
    fontWeight: '600',
    marginLeft: 2,
  },

  // Shake section styling
  shakeOuterCircle: {
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(232, 51, 51, 0.3)',
    shadowColor: 'rgba(232, 51, 51, 0.4)',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 15,
    elevation: 10,
  },
  shakeButtonDisabled: {
    backgroundColor: '#ffa726',
    shadowColor: '#ffa726',
  },
  shakeText: {
    color: '#fff',
    marginTop: 8,
    fontWeight: 'bold',
    fontSize: 16,
    letterSpacing: 1.5,
  },
  bubbleContainer: {
    height: 50, // Fixed height to prevent layout shifts
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 25,
    marginBottom: 20,
  },
  searchingBubble: {
    backgroundColor: 'rgba(189, 186, 186, 0.1)',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 30,
    borderWidth: 1,
    borderColor: 'rgba(173, 69, 69, 0.3)',
  },
  searchingText: {
    fontSize: 16,
    color: '#e93636ff',
    fontWeight: '500',
  },

  // Matches section styling
  matchesContainer: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.38)',
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 20,
    borderRadius: 20,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  matchesListContent: {
    paddingBottom: 10,
  },
  emptyMatchesCard: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
    borderRadius: 15,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  matchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: theme.colors.bgPrimary,
    borderRadius: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 1,
    borderColor: theme.colors.borderPrimary,
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: 'hidden',
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 25,
  },
  avatarText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  matchNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  friendBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `rgba(${theme.colors.primary.replace('#', '').match(/.{2}/g).map(hex => parseInt(hex, 16)).join(', ')}, 0.1)`,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    gap: 3,
  },
  friendBadgeText: {
    fontSize: 11,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  verificationBadgeSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 6,
  },
  goldBadgeSmall: {
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    borderWidth: 1,
    borderColor: '#FFD700',
  },
  blueBadgeSmall: {
    backgroundColor: 'rgba(78, 154, 241, 0.1)',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  verificationTextSmall: {
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 3,
  },
  goldTextSmall: {
    color: '#FFD700',
  },
  blueTextSmall: {
    color: theme.colors.primary,
  },
  matchDetailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  matchTimestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  matchTimestamp: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  expiryWarning: {
    fontSize: 12,
    color: '#ff3b30',
    fontWeight: '500',
    marginLeft: 4,
  },
  matchDistance: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  unreadBadge: {
    backgroundColor: '#ff3b30',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
    marginRight: 10,
  },
  unreadBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  chatButton: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    backgroundColor: theme.colors.bgSecondary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.borderPrimary,
  },
  profileIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileIconImage: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
  },
});

// ===== CHAT SCREEN STYLES =====
export const chatStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.bgSecondary,
    paddingBottom: Platform.OS === 'ios' ? 20 : 0,
  },
  headerButton: {
    padding: 10,
  },
  profileButton: {
    marginRight: 15,
    padding: 5,
  },
  profileContainer: {
    position: 'relative',
  },
  profileImage: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: theme.colors.bgSecondary,
    borderWidth: 2,
    borderColor: '#fff',
  },
  defaultAvatar: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
  },
  verificationIndicator: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.textWhite,
  },
  goldIndicator: {
    backgroundColor: '#FFD700',
  },
  blueIndicator: {
    backgroundColor: theme.colors.primary,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 10,
    backgroundColor: theme.colors.bgSecondary,
  },
  messagesList: {
    paddingBottom: 20,
  },
  messageContainer: {
    marginVertical: 3,
    maxWidth: '85%',
  },
  sentMessage: {
    alignSelf: 'flex-end',
  },
  receivedMessage: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sentBubble: {
    backgroundColor: theme.colors.primary,
    borderBottomRightRadius: 6,
  },
  receivedBubble: {
    backgroundColor: theme.colors.bgPrimary,
    borderBottomLeftRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.borderPrimary,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  sentText: {
    color: theme.colors.textWhite,
  },
  receivedText: {
    color: theme.colors.textPrimary,
  },
  messageTime: {
    fontSize: 12,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  sentTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  receivedTime: {
    color: theme.colors.textSecondary,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.bgPrimary,
    borderTopWidth: 1,
    borderTopColor: theme.colors.borderPrimary,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.borderSecondary,
    borderRadius: 22,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    maxHeight: 100,
    fontSize: 16,
    backgroundColor: theme.colors.bgSecondary,
    color: theme.colors.textPrimary,
  },
  sendButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 22,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  sendButtonDisabled: {
    backgroundColor: theme.colors.textLight,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.textError,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.textLight,
    textAlign: 'center',
  },

  // Additional ChatScreen styles that were missing
  profileButton: {
    marginRight: 15,
    padding: 5,
  },
  profileContainer: {
    position: 'relative',
  },
  ownMessageContainer: {
    alignSelf: 'flex-end',
  },
  otherMessageContainer: {
    alignSelf: 'flex-start',
  },
  ownMessageBubble: {
    backgroundColor: theme.colors.primary,
    borderBottomRightRadius: 6,
  },
  otherMessageBubble: {
    backgroundColor: theme.colors.bgPrimary,
    borderBottomLeftRadius: 6,
  },
  ownMessageText: {
    color: theme.colors.textWhite,
  },
  ownMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  otherMessageTime: {
    color: theme.colors.textSecondary,
  },
  backButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 15,
  },
  backButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: '600',
  },
  blockedBanner: {
    backgroundColor: '#ff4757',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  blockedText: {
    color: theme.colors.textWhite,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  disabledInput: {
    backgroundColor: theme.colors.bgTertiary,
    color: theme.colors.textLight,
  },
});

// ===== MATCH SCREEN STYLES =====
export const matchStyles = StyleSheet.create({
  matchContainer: {
    flex: 1,
    backgroundColor: theme.colors.bgPrimary,
  },
  matchContent: {
    alignItems: 'center',
    padding: 30,
    zIndex: 2,
  },
  matchTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: 10,
  },
  matchSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: theme.colors.textSecondary,
  },
  loader: {
    marginVertical: 20,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 20,
    width: 150,
    height: 150,
    borderRadius: 75,
    overflow: 'hidden',
    ...theme.shadows.lg,
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  defaultAvatar: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
  },
  avatarText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: theme.colors.textWhite,
  },
  verificationBadge: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  goldBadge: {
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    borderWidth: 1,
    borderColor: theme.colors.gold,
  },
  blueBadge: {
    backgroundColor: `rgba(${theme.colors.primary.replace('#', '').match(/.{2}/g).map(hex => parseInt(hex, 16)).join(', ')}, 0.1)`,
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  verificationText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  goldText: {
    color: theme.colors.gold,
  },
  blueText: {
    color: theme.colors.primary,
  },
  matchName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 5,
    textAlign: 'center',
  },
  matchAge: {
    fontSize: 18,
    color: theme.colors.textSecondary,
    marginBottom: 10,
  },
  distanceText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 15,
  },
  descriptionText: {
    fontSize: 16,
    textAlign: 'center',
    color: theme.colors.textPrimary,
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  passionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 10,
  },
  passionTag: {
    backgroundColor: theme.colors.primaryLight,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    margin: 4,
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  passionText: {
    color: theme.colors.primary,
    fontSize: 14,
  },
  chatButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 30,
    marginBottom: 15,
    width: '80%',
    alignItems: 'center',
  },
  chatButtonText: {
    color: theme.colors.textWhite,
    fontSize: 18,
    fontWeight: '600',
  },
  viewProfileButton: {
    borderWidth: 1,
    borderColor: theme.colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 30,
    marginBottom: 15,
    width: '80%',
    alignItems: 'center',
  },
  viewProfileText: {
    color: theme.colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  laterButton: {
    paddingVertical: 10,
  },
  laterText: {
    color: theme.colors.textSecondary,
    fontSize: 16,
  },
  floatingShape: {
    position: 'absolute',
    backgroundColor: 'rgba(232, 51, 51, 0.1)',
    zIndex: 1,
  },
  floatingShape1: {
    width: 200,
    height: 200,
    borderRadius: 100,
    top: 20,
    right: -100,
  },
  floatingShape2: {
    width: 150,
    height: 150,
    borderRadius: 20,
    bottom: 20,
    left: -75,
  },
  floatingShape3: {
    width: 100,
    height: 100,
    borderRadius: 50,
    top: '40%',
    left: -50,
  },
});

// ===== PROFILE SCREEN STYLES =====
export const profileStyles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.bgSecondary,
  },
  scrollContentContainer: {
    paddingBottom: Platform.OS === 'ios' ? 100 : 30,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  header: {
    padding: 20,
    backgroundColor: theme.colors.bgPrimary,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderPrimary,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  requiredNote: {
    fontSize: 14,
    color: theme.colors.textError,
    marginTop: 5,
  },
  requiredStar: {
    color: theme.colors.textError,
    fontWeight: 'bold',
  },
  section: {
    backgroundColor: theme.colors.bgPrimary,
    marginHorizontal: 15,
    marginTop: 15,
    borderRadius: 12,
    padding: 20,
    ...theme.shadows.sm,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
    color: theme.colors.textPrimary,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  photoSlot: {
    width: '48%',
    aspectRatio: 1,
    marginBottom: 10,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: theme.colors.bgSecondary,
  },
  photoImage: {
    width: '100%',
    height: '100%',
  },
  addImageButton: {
    width: '100%',
    height: '100%',
    backgroundColor: theme.colors.bgTertiary,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: theme.colors.borderSecondary,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  formGroup: {
    marginBottom: 30,
    zIndex: 1,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: theme.colors.textPrimary,
  },
  input: {
    backgroundColor: theme.colors.bgTertiary,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.borderSecondary,
    padding: 8,
    fontSize: 14,
  },
  inputError: {
    borderColor: theme.colors.borderError,
    borderWidth: 1,
  },
  errorText: {
    color: theme.colors.textError,
    fontSize: 12,
    marginTop: 5,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  charCount: {
    alignSelf: 'flex-end',
    fontSize: 12,
    color: theme.colors.textLight,
    marginTop: 5,
  },
  helperText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 5,
  },
  buttonContainer: {
    padding: 20,
    marginBottom: 30,
  },
  saveButton: {
    backgroundColor: theme.colors.primary,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: theme.colors.textWhite,
    fontSize: 18,
    fontWeight: '600',
  },
  keyboardPadding: {
    height: 60,
  },
  // Age dropdown styles
  dropdownInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Platform.OS === 'ios' ? 12 : 10,
  },
  dropdownText: {
    fontSize: 14,
    color: '#2c384a',
    fontWeight: '500',
  },
  placeholderText: {
    color: '#8a9cb0',
    fontWeight: '400',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.bgPrimary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderPrimary,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.textPrimary,
  },
  modalCloseButton: {
    padding: 5,
  },
  modalCloseText: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  modalList: {
    maxHeight: 300,
  },
  modalItem: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderPrimary,
  },
  modalItemText: {
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  // Passion tags styles
  passionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  passionTag: {
    backgroundColor: theme.colors.bgTertiary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    margin: 2,
    borderWidth: 1,
    borderColor: theme.colors.borderSecondary,
  },
  selectedPassionTag: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  passionTagText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  selectedPassionTagText: {
    color: theme.colors.textWhite,
    fontWeight: '600',
  },
  disabledPassionTag: {
    backgroundColor: '#f8f8f8',
    borderColor: '#e8e8e8',
    opacity: 0.5,
  },
  disabledPassionTagText: {
    color: '#ccc',
  },
  passionStatus: {
    marginTop: 10,
    alignItems: 'center',
  },
  selectedCount: {
    fontSize: 12,
    color: theme.colors.primary,
    marginTop: 8,
    fontWeight: '500',
  },
  // Verification styles
  verificationSection: {
    alignItems: 'center',
    padding: 20,
  },
  verificationStatus: {
    alignItems: 'center',
    marginBottom: 20,
  },
  verificationIcon: {
    marginBottom: 10,
  },
  verificationTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
    textAlign: 'center',
  },
  verificationSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  verificationButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 15,
  },
  verificationButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: '600',
  },
  verificationPending: {
    backgroundColor: '#ffa500',
  },
  verificationApproved: {
    backgroundColor: theme.colors.textSuccess,
  },
  verificationRejected: {
    backgroundColor: theme.colors.textError,
  },
  // Photo capture styles
  cameraContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  camera: {
    flex: 1,
  },
  cameraControls: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: theme.colors.textWhite,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    padding: 15,
  },
  cancelButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
  },
  instructionOverlay: {
    position: 'absolute',
    top: 100,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 20,
    borderRadius: 10,
  },
  instructionText: {
    color: theme.colors.textWhite,
    fontSize: 18,
    textAlign: 'center',
    fontWeight: '600',
  },
  instructionSubtext: {
    color: theme.colors.textWhite,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 10,
    opacity: 0.8,
  },
  // Retry button styles
  newRetryButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 180,
    ...theme.shadows.md,
  },
  newRetryButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: theme.colors.textWhite,
    marginLeft: 8,
  },
  rejectionMessage: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 15,
    marginBottom: 15,
    lineHeight: 22,
    fontStyle: 'italic',
  },
  rejectionGesture: {
    fontSize: 28,
    textAlign: 'center',
    marginBottom: 20,
    color: theme.colors.primary,
    fontWeight: '700',
  },

  // Missing styles for ProfileScreen
  sectionSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 15,
  },
  verificationContainer: {
    alignItems: 'center',
    padding: 20,
  },
  rejectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.textError,
    marginBottom: 10,
    textAlign: 'center',
  },
  rejectionReasonLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.textSecondary,
    marginBottom: 5,
    textAlign: 'center',
  },
  rejectionIconContainer: {
    marginBottom: 10,
  },
  instructionModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionModalContent: {
    backgroundColor: theme.colors.bgPrimary,
    borderRadius: 20,
    padding: 20,
    width: '90%',
    maxWidth: 400,
  },
  instructionModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  instructionModalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.textPrimary,
  },
  instructionContent: {
    alignItems: 'center',
    marginBottom: 20,
  },
  instructionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    backgroundColor: theme.colors.bgSecondary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
  },
  fingerRequirementDisplay: {
    alignItems: 'center',
    marginBottom: 20,
  },
  fingerInstructionText: {
    fontSize: 16,
    color: theme.colors.textPrimary,
    textAlign: 'center',
    marginTop: 10,
  },
  verificationTips: {
    marginTop: 20,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 10,
  },
  tipRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },

  // Additional missing styles
  rejectionContainer: {
    alignItems: 'center',
    padding: 20,
  },
  rejectionHeader: {
    alignItems: 'center',
    marginBottom: 15,
  },
  rejectionReasonText: {
    fontSize: 14,
    color: theme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: 20,
  },
  notVerifiedContainer: {
    alignItems: 'center',
    padding: 20,
  },
  verificationIconContainer: {
    marginBottom: 15,
  },
  verificationModalContainer: {
    flex: 1,
    backgroundColor: theme.colors.bgPrimary,
  },
  verificationModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderPrimary,
  },

  // Final missing styles
  verificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  refreshButton: {
    padding: 8,
  },
  verifiedContainer: {
    alignItems: 'center',
    padding: 20,
  },
  verifiedHeader: {
    alignItems: 'center',
    marginBottom: 15,
  },
  verificationModalContent: {
    flex: 1,
  },
  verificationModalScrollContent: {
    padding: 20,
  },

  // Additional missing ProfileScreen styles
  fingerDisplayContainer: {
    alignItems: 'center',
    padding: 20,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  imageBox: {
    width: '48%',
    aspectRatio: 1,
    marginBottom: 10,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imageWrapper: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  reorderButtons: {
    position: 'absolute',
    bottom: 5,
    left: 0,
    right: 0,
    height: 30,
  },
  reorderButton: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  reorderButtonLeft: {
    left: 5,
  },
  reorderButtonRight: {
    right: 5,
  },
  removeButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoNumber: {
    position: 'absolute',
    top: 5,
    left: 5,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  photoNumberText: {
    color: theme.colors.textWhite,
    fontSize: 12,
    fontWeight: '600',
  },
  verifiedText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.textSuccess,
    marginBottom: 5,
  },
  verifiedDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  verifyButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 15,
  },
  verifyButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: '600',
  },
  requestStatusContainer: {
    alignItems: 'center',
    padding: 20,
  },
  requestHeader: {
    alignItems: 'center',
    marginBottom: 15,
  },
  requestStatusText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffa500',
    marginTop: 10,
  },
  requestDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },

  // Age modal styles
  ageList: {
    maxHeight: 300,
  },
  ageOption: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderPrimary,
  },
  selectedAgeOption: {
    backgroundColor: theme.colors.primaryLight,
  },
  ageOptionText: {
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  selectedAgeOptionText: {
    color: theme.colors.primary,
    fontWeight: '600',
  },

  // Button styles
  cancelButtonText: {
    color: theme.colors.textSecondary,
    fontSize: 16,
    fontWeight: '600',
  },
  takePhotoButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    flex: 1,
    marginLeft: 10,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  takePhotoButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: '600',
  },

  // Animation styles
  fingerAnimationOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fingerAnimationContainer: {
    alignItems: 'center',
  },
  fingerDisplay: {
    marginBottom: 20,
  },
  cameraIcon: {
    marginTop: 20,
  },

  // Header styles
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  submitButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
  },

  // Photo preview styles
  photoPreviewContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  photoPreview: {
    width: 200,
    height: 200,
    borderRadius: 10,
  },

  // Tips styles
  tipText: {
    fontSize: 14,
    color: theme.colors.textPrimary,
    marginLeft: 8,
  },

  // Warning and success text
  warningText: {
    color: theme.colors.textError,
  },
  successText: {
    color: theme.colors.textSuccess,
  },

  // Badge styles
  badgeText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  goldText: {
    color: theme.colors.gold,
  },
  blueText: {
    color: theme.colors.primary,
  },
});

// ===== SETTINGS SCREEN STYLES =====
export const settingsStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.bgSecondary,
  },
  section: {
    backgroundColor: theme.colors.bgPrimary,
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 15,
    marginTop: 15,
    ...theme.shadows.sm,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  // Location services styles
  locationStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    backgroundColor: theme.colors.bgSecondary,
    padding: 12,
    borderRadius: 8,
  },
  locationIcon: {
    marginRight: 10,
  },
  locationStatusText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.textPrimary,
  },
  locationPermissionButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    marginTop: 10,
  },
  locationPermissionButtonText: {
    color: theme.colors.textWhite,
    fontSize: 14,
    fontWeight: '600',
  },
  // Premium section styles
  premiumSection: {
    backgroundColor: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
    borderWidth: 2,
    borderColor: theme.colors.gold,
  },
  premiumHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  premiumTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.gold,
    marginLeft: 10,
  },
  premiumFeature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  premiumFeatureText: {
    fontSize: 14,
    color: theme.colors.textPrimary,
    marginLeft: 10,
  },
  premiumButton: {
    backgroundColor: theme.colors.gold,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 15,
  },
  premiumButtonText: {
    color: theme.colors.textPrimary,
    fontSize: 16,
    fontWeight: '600',
  },
  // Settings item styles
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderPrimary,
  },
  settingItemLast: {
    borderBottomWidth: 0,
  },
  // Theme selection styles
  themeOption: {
    paddingVertical: 15,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: theme.colors.bgSecondary,
    borderWidth: 1,
    borderColor: theme.colors.borderSecondary,
  },
  themeOptionSelected: {
    backgroundColor: theme.colors.primaryLight,
    borderColor: theme.colors.primary,
  },
  themeOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  themeOptionText: {
    fontSize: 16,
    color: theme.colors.textPrimary,
    fontWeight: '500',
  },
  themeOptionTextSelected: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 15,
  },
  settingText: {
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  settingValue: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginRight: 10,
  },
  // Range slider styles
  sliderContainer: {
    marginVertical: 15,
  },
  sliderLabel: {
    fontSize: 14,
    color: theme.colors.textPrimary,
    marginBottom: 10,
  },
  sliderValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
    textAlign: 'center',
    marginTop: 10,
  },
  // Toggle switch styles
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
  },
  switchLabel: {
    fontSize: 16,
    color: theme.colors.textPrimary,
    flex: 1,
  },
  // Logout button
  logoutButton: {
    backgroundColor: theme.colors.textError,
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  logoutButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: '600',
  },

  // Additional SettingsScreen styles
  blockedUserItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.bgPrimary,
    borderRadius: 8,
    marginBottom: 8,
    ...theme.shadows.sm,
  },
  blockedUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  blockedUserAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  blockedUserAvatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
  },
  blockedUserAvatarText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: 'bold',
  },
  blockedUserDetails: {
    flex: 1,
  },
  blockedUserName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
  },
  blockedUserAge: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  unblockButton: {
    backgroundColor: theme.colors.textSuccess,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  unblockButtonText: {
    color: theme.colors.textWhite,
    fontSize: 14,
    fontWeight: '600',
  },

  // Location styles
  locationIcon: {
    marginRight: 10,
  },
  locationDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 10,
    lineHeight: 20,
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 15,
  },
  locationButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },

  // Premium styles
  premiumHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  premiumHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 10,
  },
  premiumBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ff9d00ff',
    marginLeft: 4,
  },
  premiumInfo: {
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
  },
  premiumInfoText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 5,
  },
  premiumDaysText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  premiumFeature: {
    marginBottom: 20,
  },
  featureHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginLeft: 8,
  },
  featureDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 15,
    lineHeight: 20,
  },
  customLocationActive: {
    backgroundColor: theme.colors.bgSecondary,
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
  },
  customLocationText: {
    fontSize: 14,
    color: theme.colors.textPrimary,
    marginBottom: 10,
  },
  locationButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  changeLocationButton: {
    backgroundColor: theme.colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    flex: 1,
    justifyContent: 'center',
  },
  changeLocationButtonText: {
    color: theme.colors.textWhite,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  disableLocationButton: {
    backgroundColor: theme.colors.textSecondary,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    flex: 1,
    alignItems: 'center',
  },
  disableLocationButtonText: {
    color: theme.colors.textWhite,
    fontSize: 14,
    fontWeight: '600',
  },
  customLocationInactive: {
    marginBottom: 15,
  },
  enableLocationButton: {
    backgroundColor: theme.colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: 'center',
  },
  enableLocationButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },

  // Premium upgrade styles
  premiumUpgrade: {
    alignItems: 'center',
    padding: 20,
  },
  authWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 15,
  },
  authWarningText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF3B30',
    marginLeft: 8,
  },
  authWarningDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  upgradeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginBottom: 20,
  },
  upgradeButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  premiumFeaturesList: {
    alignItems: 'center',
  },
  upgradeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 10,
  },
  upgradeDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },

  // Settings item styles
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textPrimary,
  },
  settingValue: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  slider: {
    width: '100%',
    height: 40,
    marginVertical: 10,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
  },
  sliderLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.borderPrimary,
    marginVertical: 20,
  },
  settingDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 10,
    lineHeight: 18,
  },

  // Gender selection styles
  genderContainer: {
    flexDirection: 'row',
    marginTop: 10,
    gap: 10,
  },
  genderButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.borderSecondary,
    backgroundColor: theme.colors.bgSecondary,
  },
  genderButtonSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  genderButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
    marginLeft: 6,
  },
  genderButtonTextSelected: {
    color: theme.colors.textWhite,
  },

  // Toggle styles
  settingToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
  },
  toggleInfo: {
    flex: 1,
  },
  toggleDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  customToggle: {
    flexDirection: 'row',
    backgroundColor: theme.colors.bgSecondary,
    borderRadius: 20,
    padding: 2,
  },
  toggleOption: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 18,
    minWidth: 80,
    alignItems: 'center',
  },
  toggleOptionActive: {
    backgroundColor: theme.colors.primary,
  },

  // Blocked users
  noBlockedText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 20,
  },

  // Modal styles
  modalContainer: {
    backgroundColor: theme.colors.bgPrimary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderPrimary,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.textPrimary,
  },
  modalHeaderSpacer: {
    width: 24,
  },
  modalContent: {
    padding: 20,
  },
  modalDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
    lineHeight: 20,
  },
  inputGroup: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.borderSecondary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: theme.colors.bgSecondary,
  },
  saveLocationButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  saveLocationButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: '600',
  },

  // Map modal styles
  mapModalContainer: {
    flex: 1,
    backgroundColor: theme.colors.bgPrimary,
  },
  mapModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderPrimary,
  },
  confirmMapButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  confirmMapButtonText: {
    color: theme.colors.textWhite,
    fontSize: 14,
    fontWeight: '600',
  },
  interactiveMapContainer: {
    flex: 1,
  },
  mapWebView: {
    flex: 1,
  },
  mapLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapLoadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  mapFooter: {
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: theme.colors.borderPrimary,
    backgroundColor: theme.colors.bgSecondary,
  },
  selectedLocationText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textPrimary,
    textAlign: 'center',
  },
  mapInstructionsText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
});

// ===== VIEW PROFILE SCREEN STYLES =====
export const viewProfileStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.bgSecondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 20,
  },
  debugText: {
    fontSize: 12,
    color: theme.colors.textLight,
    marginTop: 5,
  },
  photoSection: {
    position: 'relative',
    width: '100%',
    height: 400,
    backgroundColor: theme.colors.borderPrimary,
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  noImageContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.borderPrimary,
  },
  avatarCircle: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: theme.colors.textWhite,
  },
  verificationBadge: {
    position: 'absolute',
    top: 20,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    ...theme.shadows.sm,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 1)',
  },
  goldBadge: {
    backgroundColor: 'rgba(255, 215, 0, 0.95)',
    borderColor: '#FFD700',
  },
  blueBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: theme.colors.primary,
  },
  verificationText: {
    fontSize: 13,
    fontWeight: '700',
    marginLeft: 4,
  },
  goldText: {
    color: '#B8860B', // Dark gold for readability
  },
  blueText: {
    color: theme.colors.primary, // Use theme primary color
  },
  whiteText: {
    color: theme.colors.textWhite,
  },
  infoSection: {
    backgroundColor: theme.colors.bgPrimary,
    padding: 20,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  nameText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  profileName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
  },
  profileAge: {
    fontSize: 24,
    color: theme.colors.textSecondary,
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 10,
  },
  passionTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  profileDistance: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 15,
  },
  profileDescription: {
    fontSize: 16,
    color: theme.colors.textPrimary,
    lineHeight: 22,
    marginBottom: 20,
  },
  passionsSection: {
    marginBottom: 20,
  },
  passionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 10,
  },
  passionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  passionTag: {
    backgroundColor: theme.colors.primaryLight,
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
    marginRight: 10,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  passionText: {
    color: theme.colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  buttonContainer: {
    marginHorizontal: 20,
    marginVertical: 20,
  },
  debugButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
  },
  debugContainer: {
    backgroundColor: theme.colors.bgTertiary,
    padding: 15,
    borderRadius: 10,
    marginTop: 20,
    marginBottom: 20,
    width: '100%',
  },
  debugButtonText: {
    color: theme.colors.textSecondary,
    fontSize: 14,
    marginLeft: 8,
  },
  debugTitle: {
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 10,
    color: theme.colors.textSecondary,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.textError,
    marginTop: 10,
    marginBottom: 10,
  },
  errorMessage: {
    fontSize: 16,
    color: theme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: 20,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  actionButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 10,
    margin: 5,
    flex: 1,
    alignItems: 'center',
  },
  actionButtonText: {
    color: theme.colors.textWhite,
    fontWeight: '600',
  },
  // Image navigation styles
  imageNavButton: {
    position: 'absolute',
    bottom: 30,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  prevButton: {
    left: 20,
  },
  nextButton: {
    right: 20,
  },
  imageDots: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  imageDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    marginHorizontal: 5,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  activeImageDot: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderColor: 'rgba(255, 255, 255, 1)',
    transform: [{ scale: 1.2 }],
  },
  imageCounter: {
    position: 'absolute',
    top: 20,
    left: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  imageCounterText: {
    color: theme.colors.textWhite,
    fontSize: 14,
    fontWeight: '600',
  },
  profileNotFoundContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 149, 0, 0.1)',
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
  },
  profileNotFoundText: {
    fontSize: 14,
    color: '#ff9500',
    marginLeft: 10,
    flex: 1,
  },
  descriptionContainer: {
    marginBottom: 20,
  },
  descriptionText: {
    fontSize: 16,
    color: theme.colors.textPrimary,
    lineHeight: 22,
  },
  emptyDescriptionContainer: {
    padding: 15,
    backgroundColor: theme.colors.bgTertiary,
    borderRadius: 10,
    marginBottom: 15,
  },
  emptyText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
  },
});

// ===== REGISTER SCREEN STYLES =====
export const registerStyles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.primary,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 25,
    paddingTop: Platform.OS === 'ios' ? 20 : 0,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
    zIndex: -2,
  },
  floatingShape: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 80,
    zIndex: -1,
  },
  floatingShape1: {
    width: 200,
    height: 200,
    top: 20,
    right: -100,
    transform: [{ rotate: '30deg' }],
  },
  floatingShape2: {
    width: 180,
    height: 180,
    top: '65%',
    left: -90,
    transform: [{ rotate: '15deg' }],
  },
  floatingShape3: {
    width: 120,
    height: 120,
    top: '20%',
    left: -60,
    transform: [{ rotate: '45deg' }],
  },
  floatingShape4: {
    width: 100,
    height: 100,
    top: '80%',
    right: -50,
    transform: [{ rotate: '60deg' }],
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 15,
  },
  appTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.textWhite,
    textAlign: 'center',
    marginBottom: 5,
  },
  appSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    ...theme.shadows.lg,
  },
  welcomeText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 5,
    textAlign: 'center',
  },
  welcomeSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 25,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
    marginBottom: 12,
  },
  input: {
    backgroundColor: theme.colors.bgSecondary,
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 16,
    fontSize: 14,
    borderWidth: 1,
    borderColor: theme.colors.borderSecondary,
  },
  registerButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 30,
    width: '100%',
    alignItems: 'center',
    marginTop: 15,
    ...theme.shadows.sm,
  },
  registerButtonText: {
    color: theme.colors.textWhite,
    fontSize: 16,
    fontWeight: '600',
  },
  switchModeContainer: {
    marginTop: 15,
    alignItems: 'center',
  },
  switchModeText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  switchModeButton: {
    marginTop: 3,
  },
  switchModeButtonText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  errorText: {
    color: theme.colors.textError,
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8,
  },
  loadingContainer: {
    marginTop: 15,
  },
});

// ===== UTILITY FUNCTIONS =====
export const getThemeColor = (colorName) => {
  return theme.colors[colorName] || colorName;
};

export const getThemeSpacing = (spacingName) => {
  return theme.spacing[spacingName] || spacingName;
};

export const getThemeFontSize = (sizeName) => {
  return theme.typography.fontSize[sizeName] || sizeName;
};

export const getThemeShadow = (shadowName) => {
  return theme.shadows[shadowName] || {};
};

// ===== EXPORT ALL STYLES =====
export default {
  theme,
  commonStyles,
  loginStyles,
  homeStyles,
  chatStyles,
  matchStyles,
  profileStyles,
  settingsStyles,
  getThemeColor,
  getThemeSpacing,
  getThemeFontSize,
  getThemeShadow,
};