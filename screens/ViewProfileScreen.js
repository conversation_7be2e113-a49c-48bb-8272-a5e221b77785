// screens/ViewProfileScreen.js
import { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  ActivityIndicator,
  TouchableOpacity,
  RefreshControl,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { viewProfileStyles, theme } from './ScreensStyles';
import { useThemedStyles, useThemeColors } from '../contexts/ThemeContext';



const SERVER_ADDRESS = '**************:3000'; // Hardcoded server address

const ViewProfileScreen = ({ route, navigation }) => {
  // Force re-render when theme changes
  useThemedStyles();
  // Get current theme colors
  const themeColors = useThemeColors();

  // Extract params with defaults
  const { userId, username } = route.params || {};
  const forceRefresh = route.params?.forceRefresh || false;

  // Component state
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [errorMessage, setErrorMessage] = useState('');


  // Set the header title to the username and add menu button
  useEffect(() => {
    navigation.setOptions({
      title: username || 'Profile',
      headerRight: () => (
        <TouchableOpacity
          style={{ marginRight: 15 }}
          onPress={handleProfileOptions}
        >
          <Ionicons name="ellipsis-horizontal" size={24} color="#333" />
        </TouchableOpacity>
      ),
    });
  }, [navigation, username, userId]);

  // Fetch profile data when component mounts or forceRefresh changes
  useEffect(() => {
    fetchProfileData(forceRefresh);
  }, [forceRefresh]);

  // Pull-to-refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchProfileData(true)
      .finally(() => setRefreshing(false));
  }, []);

  const fetchProfileData = async (skipCache = false) => {
    try {
      // Reset error message
      setErrorMessage('');

      if (!userId) {
        setErrorMessage('Missing user ID');
        setLoading(false);
        return;
      }

      // Check for cached profile first (if not skipping cache)
      if (!skipCache) {
        try {
          const cachedProfile = await AsyncStorage.getItem(`profile_${userId}`);

          if (cachedProfile) {
            const parsedProfile = JSON.parse(cachedProfile);

            if (parsedProfile) {
              setProfile(parsedProfile);

              // If we have a valid cache, we can show it right away
              // while we fetch the latest in the background
              setLoading(false);
            }
          }
        } catch (cacheError) {
          console.error('Error reading cache:', cacheError);
        }
      }

      // Try to fetch updated profile from server
      if (!skipCache) setLoading(true);

      // Add a timestamp to prevent caching issues
      const timestamp = new Date().getTime();
      const url = `http://${SERVER_ADDRESS}/api/profile/${userId}?t=${timestamp}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
        },
      });

      if (response.ok) {
        const responseText = await response.text();

        try {
          const data = JSON.parse(responseText);

          if (data.profile) {
            // Add lastUpdated timestamp to the profile
            const profileWithTimestamp = {
              ...data.profile,
              lastUpdated: new Date().toISOString()
            };

            setProfile(profileWithTimestamp);

            // Cache the updated profile
            try {
              await AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(profileWithTimestamp));
            } catch (cacheError) {
              console.error('Error saving to cache:', cacheError);
            }
          } else {
            setErrorMessage('Server returned empty profile data');
          }
        } catch (parseError) {
          console.error('Error parsing server response:', parseError, 'Response:', responseText);
          setErrorMessage(`Error parsing server response: ${parseError.message}`);
        }
      } else if (response.status === 404) {
        setErrorMessage('Profile not found on server');

        // Create a basic profile with the username
        if (username) {
          const basicProfile = {
            username,
            images: [],
            description: '',
            passions: [],
            age: null,
            lastUpdated: new Date().toISOString()
          };
          setProfile(basicProfile);
        }
      } else {
        const errorText = await response.text();
        console.error('Server error:', response.status, errorText);
        setErrorMessage(`Server error: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      setErrorMessage(`Error fetching profile: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle image navigation
  const nextImage = () => {
    if (profile && profile.images && profile.images.length > 0) {
      setActiveImageIndex((prevIndex) =>
        prevIndex === profile.images.length - 1 ? 0 : prevIndex + 1
      );
    }
  };

  const prevImage = () => {
    if (profile && profile.images && profile.images.length > 0) {
      setActiveImageIndex((prevIndex) =>
        prevIndex === 0 ? profile.images.length - 1 : prevIndex - 1
      );
    }
  };



  // Handle profile options menu
  const handleProfileOptions = () => {
    Alert.alert(
      "Profile Options",
      "What would you like to do?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Block User",
          style: "destructive",
          onPress: handleBlockUser
        },
        {
          text: "Report User",
          style: "destructive",
          onPress: handleReportUser
        }
      ]
    );
  };

  // Handle delete chat functionality
  const handleDeleteChat = () => {
    Alert.alert(
      "Delete Chat",
      `Are you sure you want to delete your chat with ${username}? You can match with this user again later.`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              const currentUserJSON = await AsyncStorage.getItem('user');
              if (!currentUserJSON) {
                Alert.alert('Error', 'Please log in again.');
                return;
              }

              const currentUser = JSON.parse(currentUserJSON);
              if (!currentUser?.token) {
                Alert.alert('Error', 'Please log out and log in again to refresh your session.');
                return;
              }

              // Call delete chat API or function here
              // For now, just show success and navigate back
              Alert.alert('Chat Deleted', 'The chat has been deleted successfully.');
              navigation.goBack();
            } catch (error) {
              console.error('Error deleting chat:', error);
              Alert.alert('Error', 'Failed to delete chat. Please try again.');
            }
          }
        }
      ]
    );
  };

  // Handle block user functionality
  const handleBlockUser = () => {
    Alert.alert(
      "Block User",
      `Are you sure you want to block ${username}? You won't be able to match with them again.`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Block",
          style: "destructive",
          onPress: async () => {
            try {
              const currentUserJSON = await AsyncStorage.getItem('user');
              const server = await AsyncStorage.getItem('serverAddress');

              if (!currentUserJSON || !server) {
                Alert.alert('Error', 'Please log in again.');
                return;
              }

              const currentUser = JSON.parse(currentUserJSON);
              if (!currentUser?.token) {
                Alert.alert('Error', 'Please log out and log in again to refresh your session.');
                return;
              }

              const response = await fetch(`http://${server}/api/block`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${currentUser.token}`,
                },
                body: JSON.stringify({
                  blockedUserId: userId
                })
              });

              if (response.ok) {
                Alert.alert('User Blocked', `${username} has been blocked successfully.`);
                navigation.goBack();
              } else {
                throw new Error('Failed to block user');
              }
            } catch (error) {
              console.error('Error blocking user:', error);
              Alert.alert('Error', 'Failed to block user. Please try again.');
            }
          }
        }
      ]
    );
  };

  // Handle report user functionality
  const handleReportUser = () => {
    Alert.alert(
      `Report ${username}`,
      "Why are you reporting this user?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Harassment or bullying",
          onPress: () => submitReport("Harassment or bullying")
        },
        {
          text: "Inappropriate content",
          onPress: () => submitReport("Inappropriate content")
        },
        {
          text: "Spam or fake profile",
          onPress: () => submitReport("Spam or fake profile")
        },
        {
          text: "Hate speech",
          onPress: () => submitReport("Hate speech")
        },
        {
          text: "Scam or fraud",
          onPress: () => submitReport("Scam or fraud")
        },
        {
          text: "Other",
          onPress: () => {
            Alert.prompt(
              "Report Details",
              "Please provide more details about your report:",
              [
                { text: "Cancel", style: "cancel" },
                {
                  text: "Submit",
                  onPress: (details) => submitReport("Other", details)
                }
              ],
              "plain-text"
            );
          }
        }
      ]
    );
  };

  // Submit report function
  const submitReport = async (reason, details = '') => {
    try {
      const currentUserJSON = await AsyncStorage.getItem('user');
      const server = await AsyncStorage.getItem('serverAddress');

      if (!currentUserJSON || !server) {
        Alert.alert('Error', 'Please log in again.');
        return;
      }

      const currentUser = JSON.parse(currentUserJSON);
      if (!currentUser?.token) {
        Alert.alert('Error', 'Please log out and log in again to refresh your session.');
        return;
      }

      if (!userId) {
        Alert.alert('Error', 'Unable to identify user to report.');
        return;
      }

      const response = await fetch(`http://${server}/api/report`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentUser.token}`,
        },
        body: JSON.stringify({
          reportedUserId: userId,
          reason: reason,
          details: details.trim()
        })
      });

      const data = await response.json().catch(() => ({}));

      if (!response.ok) {
        throw new Error(data.error || data.message || `HTTP ${response.status}`);
      }

      Alert.alert('Report Submitted', 'Thank you for your report. Our team will review it shortly.');

    } catch (error) {
      console.error('Report error:', error);
      Alert.alert('Error', error.message || 'Failed to submit report. Please try again.');
    }
  };

  // Render loading state
  if (loading) {
    return (
      <View style={[viewProfileStyles.loadingContainer, { backgroundColor: themeColors.bgSecondary }]}>
        <ActivityIndicator size="large" color={themeColors.primary} />
        <Text style={[viewProfileStyles.loadingText, { color: themeColors.textSecondary }]}>Loading profile...</Text>
      </View>
    );
  }

  // Render error state
  if (errorMessage) {
    return (
      <ScrollView style={[viewProfileStyles.container, { backgroundColor: themeColors.bgSecondary }]}>
        <View style={viewProfileStyles.errorContainer}>
          <Ionicons name="alert-circle" size={60} color={themeColors.primary} />
          <Text style={[viewProfileStyles.errorTitle, { color: themeColors.textPrimary }]}>Error Loading Profile</Text>
          <Text style={[viewProfileStyles.errorMessage, { color: themeColors.textSecondary }]}>{errorMessage}</Text>

          <View style={viewProfileStyles.actionButtonsContainer}>
            <TouchableOpacity
              style={viewProfileStyles.actionButton}
              onPress={() => fetchProfileData(true)}
            >
              <Text style={viewProfileStyles.actionButtonText}>Retry</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={viewProfileStyles.actionButton}
              onPress={() => navigation.goBack()}
            >
              <Text style={viewProfileStyles.actionButtonText}>Go Back</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    );
  }

  // Render the profile data
  return (
    <View style={{ flex: 1, backgroundColor: themeColors.bgSecondary }}>
      <ScrollView
        style={[viewProfileStyles.container, { backgroundColor: themeColors.bgSecondary }]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[themeColors.primary]}
            tintColor={themeColors.primary}
          />
        }
      >
      {/* Profile Photos Section */}
      <View style={viewProfileStyles.photoSection}>
        {profile && profile.images && profile.images.length > 0 ? (
          <>
            <Image
              source={{
                uri: profile.images[activeImageIndex].startsWith('data:')
                  ? profile.images[activeImageIndex]
                  : `data:image/jpeg;base64,${profile.images[activeImageIndex]}`
              }}
              style={viewProfileStyles.profileImage}
              resizeMode="cover"
            />

            {/* Image counter */}
            {profile.images.length > 1 && (
              <View style={viewProfileStyles.imageCounter}>
                <Text style={viewProfileStyles.imageCounterText}>
                  {activeImageIndex + 1}/{profile.images.length}
                </Text>
              </View>
            )}

            {/* Image navigation buttons */}
            {profile.images.length > 1 && (
              <>
                {/* Left arrow - show if not on first image */}
                {activeImageIndex > 0 && (
                  <TouchableOpacity
                    style={[viewProfileStyles.imageNavButton, viewProfileStyles.prevButton]}
                    onPress={prevImage}
                  >
                    <Ionicons name="chevron-back" size={28} color="#fff" />
                  </TouchableOpacity>
                )}

                {/* Right arrow - show if not on last image */}
                {activeImageIndex < profile.images.length - 1 && (
                  <TouchableOpacity
                    style={[viewProfileStyles.imageNavButton, viewProfileStyles.nextButton]}
                    onPress={nextImage}
                  >
                    <Ionicons name="chevron-forward" size={28} color="#fff" />
                  </TouchableOpacity>
                )}

                {/* Image dots indicator */}
                <View style={viewProfileStyles.imageDots}>
                  {profile.images.map((_, index) => (
                    <TouchableOpacity
                      key={index}
                      onPress={() => setActiveImageIndex(index)}
                      style={[
                        viewProfileStyles.imageDot,
                        index === activeImageIndex && viewProfileStyles.activeImageDot
                      ]}
                    />
                  ))}
                </View>
              </>
            )}
          </>
        ) : (
          <View style={viewProfileStyles.noImageContainer}>
            <View style={viewProfileStyles.avatarCircle}>
              <Text style={viewProfileStyles.avatarText}>
                {username ? username.charAt(0).toUpperCase() : '?'}
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* Profile Info Section */}
      <View style={[viewProfileStyles.infoSection, { backgroundColor: themeColors.bgPrimary }]}>
        <View style={viewProfileStyles.nameRow}>
          <Text style={[viewProfileStyles.nameText, { color: themeColors.textPrimary }]}>
            {username || 'Unknown User'}
            {profile?.age ? `, ${profile.age}` : ''}
          </Text>
          {profile?.verification?.isVerified && (
            <View style={[
              viewProfileStyles.verificationBadge,
              profile.badgeType === 'gold' ? viewProfileStyles.goldBadge : viewProfileStyles.blueBadge
            ]}>
              <Ionicons
                name="checkmark-circle"
                size={16}
                color={profile.badgeType === 'gold' ? '#FFD700' : themeColors.primary}
              />
              <Text style={[
                viewProfileStyles.verificationText,
                profile.badgeType === 'gold' ? viewProfileStyles.goldText : viewProfileStyles.blueText
              ]}>
                Verified
              </Text>
            </View>
          )}
        </View>

        {(!profile || Object.keys(profile).length === 0) && (
          <View style={viewProfileStyles.profileNotFoundContainer}>
            <Ionicons name="alert-circle-outline" size={24} color="#ff9500" />
            <Text style={viewProfileStyles.profileNotFoundText}>
              This user hasn't completed their profile yet
            </Text>
          </View>
        )}

        {/* Description */}
        {profile?.description ? (
          <View style={viewProfileStyles.descriptionContainer}>
            <Text style={viewProfileStyles.descriptionText}>{profile.description}</Text>
          </View>
        ) : (
          <View style={viewProfileStyles.emptyDescriptionContainer}>
            <Text style={viewProfileStyles.emptyText}>No description available</Text>
          </View>
        )}

        {/* Passions/Interests */}
        {profile?.passions && profile.passions.length > 0 ? (
          <View style={viewProfileStyles.passionsContainer}>
            <Text style={viewProfileStyles.sectionTitle}>Interests</Text>
            <View style={viewProfileStyles.passionTags}>
              {profile.passions.map((passion, index) => (
                <View key={index} style={viewProfileStyles.passionTag}>
                  <Text style={viewProfileStyles.passionText}>{passion}</Text>
                </View>
              ))}
            </View>
          </View>
        ) : null}
      </View>




      </ScrollView>


    </View>
  );
};

// Styles are now imported from ScreensStyles.js

export default ViewProfileScreen;

