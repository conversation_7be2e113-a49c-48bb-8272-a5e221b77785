// screens/SettingsScreen.js
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  TextInput,
  Alert,
  ScrollView,
  FlatList,
  Platform,
  Linking,
  Modal,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import { settingsStyles, theme } from './ScreensStyles';
import { useTheme, useThemedStyles, useThemeColors, useDynamicStyles } from '../contexts/ThemeContext';

import { WebView } from 'react-native-webview';

const SettingsScreen = ({
  navigation,
  maxDistance,
  minAge = 18,
  maxAge = 100,
  blockedUsers = [],
  onUpdateMaxDistance,
  onUpdateAgeRange,
  onUnblockUser,
  onLogout,
  user,
  serverAddress
}) => {
  // Theme context
  const { currentTheme, changeTheme, getThemeDisplayName, availableThemes } = useTheme();
  // Force re-render when theme changes
  useThemedStyles();
  // Get current theme colors
  const themeColors = useThemeColors();
  // Get dynamic styles
  const dynamicStyles = useDynamicStyles();

  // Create theme-specific styles
  const themeSpecificStyles = {
    themeOptionSelected: {
      backgroundColor: themeColors.primaryLight,
      borderColor: themeColors.primary,
    },
    themeOptionTextSelected: {
      color: themeColors.primary,
      fontWeight: '600',
    },
  };

  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [distance, setDistance] = useState(maxDistance);
  const [minimumAge, setMinimumAge] = useState(minAge);
  const [maximumAge, setMaximumAge] = useState(maxAge);
  const [blockedUsernames, setBlockedUsernames] = useState({});
  const [locationPermission, setLocationPermission] = useState(null);
  const [isCheckingLocation, setIsCheckingLocation] = useState(false);

  // Gender and matching preferences
  const [userGender, setUserGender] = useState('');
  const [interestedIn, setInterestedIn] = useState('both');
  const [lookingFor, setLookingFor] = useState('relationship'); // 'relationship' or 'friends'

  // Premium-related state
  const [premiumStatus, setPremiumStatus] = useState({
    isPremium: false,
    subscriptionType: null,
    subscriptionStart: null,
    subscriptionEnd: null,
    daysRemaining: 0,
    customLocation: {
      enabled: false,
      city: '',
      country: '',
      coordinates: null
    }
  });
  const [customLocation, setCustomLocation] = useState({
    enabled: false,
    city: '',
    country: '',
    coordinates: null
  });
  const [isLoadingPremium, setIsLoadingPremium] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [showMapModal, setShowMapModal] = useState(false);
  const [newCity, setNewCity] = useState('');
  const [newCountry, setNewCountry] = useState('');
  const [selectedMapLocation, setSelectedMapLocation] = useState(null);

  // Check location permission status and load premium status on mount
  useEffect(() => {
    checkLocationPermission();
    loadUserPreferences();
    if (user && serverAddress) {
      loadPremiumStatus();
    }
  }, [user, serverAddress]);

  // Load user preferences from storage and server
  const loadUserPreferences = async () => {
    try {
      // First load from local storage
      const savedGender = await AsyncStorage.getItem('userGender');
      const savedInterestedIn = await AsyncStorage.getItem('interestedIn');
      const savedLookingFor = await AsyncStorage.getItem('lookingFor');

      if (savedGender) setUserGender(savedGender);
      if (savedInterestedIn) setInterestedIn(savedInterestedIn);
      if (savedLookingFor) setLookingFor(savedLookingFor);

      // Then try to load from server if user is logged in
      if (user && serverAddress) {
        try {
          // Get token from user object, not AsyncStorage
          const token = user.token;

          // Ensure server address has protocol
          const fullServerAddress = serverAddress.startsWith('http') ? serverAddress : `http://${serverAddress}`;

          console.log('📋 Settings: Loading preferences from server:', `${fullServerAddress}/api/user/preferences`);
          console.log('🔑 Settings: Using token:', token ? 'Token present' : 'No token');

          const response = await fetch(`${fullServerAddress}/api/user/preferences`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          console.log('📡 Settings: Response status:', response.status);

          if (response.ok) {
            const serverPreferences = await response.json();
            console.log('✅ Settings: Server preferences loaded:', serverPreferences);

            // Update state with server data
            setUserGender(serverPreferences.gender || '');
            setInterestedIn(serverPreferences.interestedIn || 'both');
            setLookingFor(serverPreferences.lookingFor || 'relationship');

            // Update local storage with server data
            await AsyncStorage.setItem('userGender', serverPreferences.gender || '');
            await AsyncStorage.setItem('interestedIn', serverPreferences.interestedIn || 'both');
            await AsyncStorage.setItem('lookingFor', serverPreferences.lookingFor || 'relationship');
          } else {
            const errorText = await response.text();
            console.log('❌ Settings: Server response not ok:', response.status, response.statusText);
            console.log('❌ Settings: Error response body:', errorText);
          }
        } catch (serverError) {
          console.error('❌ Settings: Error loading preferences from server:', serverError);
          console.error('Settings error details:', serverError.message);
        }
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  // Save user preferences to storage and server
  const saveUserPreferences = async (gender, interested, looking) => {
    try {
      // Save to local storage
      await AsyncStorage.setItem('userGender', gender);
      await AsyncStorage.setItem('interestedIn', interested);
      await AsyncStorage.setItem('lookingFor', looking);

      // Save to server if user is logged in
      if (user && serverAddress) {
        try {
          // Get token from user object, not AsyncStorage
          const token = user.token;

          // Ensure server address has protocol
          const fullServerAddress = serverAddress.startsWith('http') ? serverAddress : `http://${serverAddress}`;

          console.log('💾 Saving preferences to server:', `${fullServerAddress}/api/user/preferences`);

          const response = await fetch(`${fullServerAddress}/api/user/preferences`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              gender: gender,
              interestedIn: interested,
              lookingFor: looking
            })
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Failed to save preferences to server:', response.status);
            console.error('❌ Error response body:', errorText);
          } else {
            console.log('✅ Preferences saved to server successfully');
          }
        } catch (serverError) {
          console.error('❌ Error saving preferences to server:', serverError);
        }
      }
    } catch (error) {
      console.error('Error saving user preferences:', error);
    }
  };

  // Check location permission
  const checkLocationPermission = async () => {
    setIsCheckingLocation(true);
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      setLocationPermission(status);
    } catch (error) {
      console.error('Error checking location permission:', error);
    } finally {
      setIsCheckingLocation(false);
    }
  };

  // Request location permission
  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(status);
      
      if (status !== 'granted') {
        // If permission denied, show alert with option to open settings
        Alert.alert(
          "Location Permission Required",
          "Shake & Match needs location permission to find matches near you. Please enable location in your device settings.",
          [
            { text: "Cancel", style: "cancel" },
            { 
              text: "Open Settings", 
              onPress: () => {
                if (Platform.OS === 'ios') {
                  Linking.openURL('app-settings:');
                } else {
                  Linking.openSettings();
                }
              } 
            }
          ]
        );
      } else {
        Alert.alert(
          "Location Enabled",
          "Location permission granted! You can now match with nearby users."
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  // Load blocked user profiles
  useEffect(() => {
    const loadBlockedUserProfiles = async () => {
      try {
        const profilesObj = {};

        for (const userId of blockedUsers) {
          // First try to get from local storage
          const cachedProfile = await AsyncStorage.getItem(`profile_${userId}`);
          if (cachedProfile) {
            try {
              profilesObj[userId] = JSON.parse(cachedProfile);
              continue;
            } catch (parseError) {
              console.log('Error parsing cached profile for user:', userId);
            }
          }

          // If not in cache or user/server available, try to fetch from server
          if (user && serverAddress) {
            try {
              const token = user.token;
              const fullServerAddress = serverAddress.startsWith('http') ? serverAddress : `http://${serverAddress}`;

              const response = await fetch(`${fullServerAddress}/api/profile/${userId}`, {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                }
              });

              if (response.ok) {
                const profile = await response.json();
                profilesObj[userId] = profile;

                // Cache the profile
                await AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(profile));
              } else {
                // Fallback to stored username or default
                const storedUsername = await AsyncStorage.getItem(`username_${userId}`);
                profilesObj[userId] = {
                  username: storedUsername || 'Unknown User',
                  images: []
                };
              }
            } catch (fetchError) {
              console.log('Error fetching profile for blocked user:', userId, fetchError.message);
              // Fallback to stored username or default
              const storedUsername = await AsyncStorage.getItem(`username_${userId}`);
              profilesObj[userId] = {
                username: storedUsername || 'Unknown User',
                images: []
              };
            }
          } else {
            // No server connection, use stored username
            const storedUsername = await AsyncStorage.getItem(`username_${userId}`);
            profilesObj[userId] = {
              username: storedUsername || 'Unknown User',
              images: []
            };
          }
        }

        setBlockedUsernames(profilesObj);
      } catch (error) {
        console.error('Error loading blocked user profiles:', error);
      }
    };

    if (blockedUsers.length > 0) {
      loadBlockedUserProfiles();
    } else {
      setBlockedUsernames({});
    }
  }, [blockedUsers, user, serverAddress]);

  // Handle unblock user button press
  const handleUnblockUser = (userId) => {
    Alert.alert(
      'Unblock User',
      `Are you sure you want to unblock ${blockedUsernames[userId] || 'this user'}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Unblock', 
          style: 'destructive',
          onPress: () => {
            if (onUnblockUser) {
              onUnblockUser(userId);
            }
          }
        }
      ]
    );
  };

  // Handle distance change
  const handleDistanceChange = (value) => {
    setDistance(value);
    onUpdateMaxDistance(value);
  };

  // Handle min age change
  const handleMinAgeChange = (value) => {
    // Set the value directly from the slider
    setMinimumAge(value);
    
    // If the minimum age is now greater than or equal to maximum age, 
    // also increase the maximum age
    if (value >= maximumAge) {
      const newMaxAge = value + 1;
      setMaximumAge(newMaxAge);
      
      if (onUpdateAgeRange) {
        onUpdateAgeRange(value, newMaxAge);
      }
    } else {
      // Otherwise just update the minimum age
      if (onUpdateAgeRange) {
        onUpdateAgeRange(value, maximumAge);
      }
    }
  };

  // Handle max age change
  const handleMaxAgeChange = (value) => {
    // Set the value directly from the slider
    setMaximumAge(value);
    
    // If the maximum age is now less than or equal to minimum age, 
    // also decrease the minimum age
    if (value <= minimumAge) {
      const newMinAge = value - 1;
      setMinimumAge(newMinAge);
      
      if (onUpdateAgeRange) {
        onUpdateAgeRange(newMinAge, value);
      }
    } else {
      // Otherwise just update the maximum age
      if (onUpdateAgeRange) {
        onUpdateAgeRange(minimumAge, value);
      }
    }
  };

  // Handle logout
  const handleLogout = () => {
    Alert.alert(
      'Confirm Logout',
      'Are you sure you want to log out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: onLogout }
      ]
    );
  };

  // Load premium status
  const loadPremiumStatus = async () => {
    console.log('Loading premium status...', {
      user: !!user,
      userId: user?.id,
      username: user?.username,
      serverAddress
    });
    if (!user || !serverAddress) {
      console.log('Missing user or serverAddress');
      return;
    }

    setIsLoadingPremium(true);
    try {
      // Try to get token from user object first (mobile app storage method)
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
        console.log('Token from user object:', !!token);
      }

      // Fallback to separate authToken storage (web app method)
      if (!token) {
        token = await AsyncStorage.getItem('authToken');
        console.log('Token from authToken key:', !!token);
      }

      if (!token) {
        console.log('No auth token found in either location');
        return;
      }

      const url = `http://${serverAddress}/api/premium/status`;
      console.log('Fetching premium status from:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('Premium status response:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Premium status data received:', JSON.stringify(data, null, 2));
        setPremiumStatus(data);
        setCustomLocation(data.customLocation || {
          enabled: false,
          city: '',
          country: '',
          coordinates: null
        });
        console.log('Premium status set:', data.isPremium);
      } else {
        const errorText = await response.text();
        console.log('Premium status error:', response.status, response.statusText, errorText);
        // Set a default premium status for testing
        setPremiumStatus({
          isPremium: false,
          customLocation: { enabled: false }
        });
      }
    } catch (error) {
      console.error('Error loading premium status:', error);
      // Set a default premium status for testing
      setPremiumStatus({
        isPremium: false,
        customLocation: { enabled: false }
      });
    } finally {
      setIsLoadingPremium(false);
    }
  };

  // Update custom location
  const updateCustomLocation = async () => {
    if (!newCity.trim() || !newCountry.trim()) {
      Alert.alert('Error', 'Please enter both city and country');
      return;
    }

    setIsLoadingPremium(true);
    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }
      if (!token) return;

      // For demo purposes, we'll use approximate coordinates
      // In a real app, you'd use a geocoding service
      const mockCoordinates = {
        latitude: 40.7128 + (Math.random() - 0.5) * 10,
        longitude: -74.0060 + (Math.random() - 0.5) * 10
      };

      const response = await fetch(`http://${serverAddress}/api/premium/location`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          city: newCity.trim(),
          country: newCountry.trim(),
          latitude: mockCoordinates.latitude,
          longitude: mockCoordinates.longitude
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setCustomLocation(data.customLocation);
        setShowLocationModal(false);
        setNewCity('');
        setNewCountry('');
        Alert.alert('Success', 'Custom location updated successfully!');
        loadPremiumStatus(); // Refresh status
      } else {
        const error = await response.json();
        Alert.alert('Error', error.error || 'Failed to update location');
      }
    } catch (error) {
      console.error('Error updating custom location:', error);
      Alert.alert('Error', 'Failed to update location');
    } finally {
      setIsLoadingPremium(false);
    }
  };

  // Disable custom location
  const disableCustomLocation = async () => {
    setIsLoadingPremium(true);
    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }
      if (!token) return;

      const response = await fetch(`http://${serverAddress}/api/premium/location`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setCustomLocation({
          enabled: false,
          city: '',
          country: '',
          coordinates: null
        });
        Alert.alert('Success', 'Custom location disabled. Using your real location now.');
        loadPremiumStatus(); // Refresh status
      } else {
        const error = await response.json();
        Alert.alert('Error', error.error || 'Failed to disable custom location');
      }
    } catch (error) {
      console.error('Error disabling custom location:', error);
      Alert.alert('Error', 'Failed to disable custom location');
    } finally {
      setIsLoadingPremium(false);
    }
  };

  // Open coordinate-based location picker
  const openCoordinateLocationPicker = async () => {
    try {
      // Get current location as default
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({});
        setSelectedMapLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude
        });
      } else {
        // Default to a central location if no permission
        setSelectedMapLocation({
          latitude: 40.7128,
          longitude: -74.0060
        });
      }
    } catch (error) {
      console.log('Error getting current location:', error);
      // Default to New York coordinates
      setSelectedMapLocation({
        latitude: 40.7128,
        longitude: -74.0060
      });
    }

    setShowMapModal(true);
  };

  // Reverse geocode selected location
  const reverseGeocodeLocation = async (latitude, longitude) => {
    try {
      const results = await Location.reverseGeocodeAsync({
        latitude,
        longitude
      });

      if (results.length > 0) {
        const result = results[0];
        return {
          city: result.city || result.subregion || result.region || 'Unknown City',
          country: result.country || 'Unknown Country'
        };
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
    }

    return {
      city: 'Custom Location',
      country: 'Unknown'
    };
  };

  // Update custom location with specific coordinates
  const updateCustomLocationWithCoords = async (city, country, latitude, longitude) => {
    if (!user || !serverAddress) return false;

    try {
      // Get token from user object
      let token = null;
      const userJSON = await AsyncStorage.getItem('user');
      if (userJSON) {
        const userData = JSON.parse(userJSON);
        token = userData.token;
      }
      if (!token) return false;

      const response = await fetch(`http://${serverAddress}/api/premium/location`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          city: city.trim(),
          country: country.trim(),
          latitude,
          longitude
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setCustomLocation(data.customLocation);
        Alert.alert('Success', `Custom location set to ${city}, ${country}`);
        loadPremiumStatus(); // Refresh status
        return true;
      } else {
        const error = await response.json();
        Alert.alert('Error', error.error || 'Failed to update location');
        return false;
      }
    } catch (error) {
      console.error('Error updating custom location:', error);
      Alert.alert('Error', 'Failed to update location');
      return false;
    }
  };

  // Handle messages from the Google Maps WebView
  const handleMapMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('Map message received:', data);

      if (data.type === 'locationSelected') {
        setSelectedMapLocation({
          latitude: data.lat,
          longitude: data.lng,
          city: data.address || 'Custom Location'
        });
      }
    } catch (error) {
      console.error('Error parsing map message:', error);
    }
  };

  // Generate HTML for OpenStreetMap (free, no API key needed)
  const getGoogleMapsHTML = () => {
    const currentLat = selectedMapLocation?.latitude || 40.7128;
    const currentLng = selectedMapLocation?.longitude || -74.0060;

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Location Picker</title>
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
        <style>
            body, html {
                margin: 0;
                padding: 0;
                height: 100%;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            #map {
                height: 100%;
                width: 100%;
            }
          .reset-button-container {
              position: absolute;
              top: 20px;
              left: 50%;
              transform: translateX(-50%);
              z-index: 1000;
          }
            #resetButton {
                background: rgba(255, 255, 255, 0.95);
                color: #333;
                border: 1px solid rgba(0, 0, 0, 0.1);
                padding: 12px 16px;
                border-radius: 12px;
                font-size: 13px;
                font-weight: 600;
                cursor: pointer;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                backdrop-filter: blur(10px);
                transition: all 0.2s ease;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            #resetButton:hover {
                background: rgba(255, 255, 255, 1);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
            }
            #resetButton:active {
                transform: translateY(0);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            #resetButton:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }
            .leaflet-control-attribution {
                font-size: 10px !important;
            }
        </style>
    </head>
    <body>
        <div class="reset-button-container">
            <button id="resetButton" onclick="resetToRealLocation()">📍 Use My Real Location</button>
        </div>
        <div id="map"></div>

        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
        <script>
            let map;
            let marker;
            let selectedLat = ${currentLat};
            let selectedLng = ${currentLng};

            // Initialize the map
            map = L.map('map').setView([selectedLat, selectedLng], 4);

            // Add OpenStreetMap tiles (free, no API key needed)
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 19
            }).addTo(map);

            // Add satellite imagery option
            const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: '© Esri, Maxar, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community',
                maxZoom: 19
            });

            // Layer control
            const baseMaps = {
                "Street Map": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }),
                "Satellite": satelliteLayer
            };

            L.control.layers(baseMaps).addTo(map);

            // Custom marker icon
            const customIcon = L.divIcon({
                html: \`
                    <div style="
                        width: 30px;
                        height: 40px;
                        position: relative;
                        filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
                    ">
                        <svg width="30" height="40" viewBox="0 0 30 40">
                            <path d="M15 0C6.716 0 0 6.716 0 15c0 15 15 25 15 25s15-10 15-25C30 6.716 23.284 0 15 0z" fill="#FF4444"/>
                            <circle cx="15" cy="15" r="7" fill="white"/>
                            <circle cx="15" cy="15" r="3" fill="#FF4444"/>
                        </svg>
                    </div>
                \`,
                className: 'custom-marker',
                iconSize: [30, 40],
                iconAnchor: [15, 40]
            });

            // Add initial marker if we have a selected location
            if (selectedLat && selectedLng) {
                marker = L.marker([selectedLat, selectedLng], {icon: customIcon}).addTo(map);
            }

            // Handle map clicks
            map.on('click', function(e) {
                const lat = e.latlng.lat;
                const lng = e.latlng.lng;

                console.log('Map clicked at:', lat, lng);

                // Remove existing marker
                if (marker) {
                    map.removeLayer(marker);
                }

                // Add new marker (don't center map - let user stay where they are)
                marker = L.marker([lat, lng], {icon: customIcon}).addTo(map);

                // Always send location data immediately
                const locationData = {
                    type: 'locationSelected',
                    lat: lat,
                    lng: lng,
                    address: 'Custom Location'
                };

                console.log('Sending location data:', locationData);
                window.ReactNativeWebView.postMessage(JSON.stringify(locationData));

                // Try to get address using Nominatim (free reverse geocoding) - this is optional
                fetch(\`https://nominatim.openstreetmap.org/reverse?format=json&lat=\${lat}&lon=\${lng}&zoom=18&addressdetails=1\`)
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.display_name) {
                            // Send updated location data with real address
                            const updatedLocationData = {
                                type: 'locationSelected',
                                lat: lat,
                                lng: lng,
                                address: data.display_name
                            };

                            console.log('Sending updated location data with address:', updatedLocationData);
                            window.ReactNativeWebView.postMessage(JSON.stringify(updatedLocationData));
                        }
                    })
                    .catch(error => {
                        console.log('Reverse geocoding failed:', error);
                        // Location data was already sent above, so this is fine
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'locationSelected',
                            lat: lat,
                            lng: lng,
                            address: 'Custom Location'
                        }));
                    });
            });

            // Add zoom controls
            L.control.zoom({
                position: 'bottomright'
            }).addTo(map);

            // Reset to real location function
            function resetToRealLocation() {
                if (navigator.geolocation) {
                    document.getElementById('resetButton').textContent = '📍 Getting location...';
                    document.getElementById('resetButton').disabled = true;

                    const options = {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 60000
                    };

                    navigator.geolocation.getCurrentPosition(function(position) {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;

                        console.log('Got real location:', lat, lng);

                        // Remove existing marker
                        if (marker) {
                            map.removeLayer(marker);
                        }

                        // Add marker at real location
                        marker = L.marker([lat, lng], {icon: customIcon}).addTo(map);

                        // Only center map on real location when using "Use My Real Location" button
                        map.setView([lat, lng], 13);

                        // Get address for real location
                        fetch(\`https://nominatim.openstreetmap.org/reverse?format=json&lat=\${lat}&lon=\${lng}&zoom=18&addressdetails=1\`)
                            .then(response => response.json())
                            .then(data => {
                                let address = 'Your Real Location';
                                if (data && data.display_name) {
                                    address = data.display_name;
                                }

                                console.log('Sending location to React Native:', { lat, lng, address });

                                // Send location data to React Native
                                window.ReactNativeWebView.postMessage(JSON.stringify({
                                    type: 'locationSelected',
                                    lat: lat,
                                    lng: lng,
                                    address: address
                                }));
                            })
                            .catch(error => {
                                console.log('Reverse geocoding failed:', error);

                                // Send location data anyway
                                window.ReactNativeWebView.postMessage(JSON.stringify({
                                    type: 'locationSelected',
                                    lat: lat,
                                    lng: lng,
                                    address: 'Your Real Location'
                                }));
                            });

                        document.getElementById('resetButton').textContent = '📍 Use My Real Location';
                        document.getElementById('resetButton').disabled = false;
                    }, function(error) {
                        console.log('Geolocation error:', error.code, error.message);
                        let errorMessage = 'Could not get your location. ';

                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage += 'Location access denied. Please enable location permissions.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage += 'Location information unavailable.';
                                break;
                            case error.TIMEOUT:
                                errorMessage += 'Location request timed out.';
                                break;
                            default:
                                errorMessage += 'Unknown error occurred.';
                                break;
                        }

                        alert(errorMessage);
                        document.getElementById('resetButton').textContent = '📍 Use My Real Location';
                        document.getElementById('resetButton').disabled = false;
                    }, options);
                } else {
                    alert('Geolocation is not supported by this browser.');
                }
            }
        </script>
    </body>
    </html>
    `;
  };

  // Confirm coordinate location selection
  const confirmCoordinateLocation = async () => {
    if (!selectedMapLocation) {
      Alert.alert('Error', 'Please select a location on the map');
      return;
    }

    setIsLoadingPremium(true);
    try {
      let locationInfo = {
        city: selectedMapLocation.city || 'Custom Location',
        country: selectedMapLocation.country || 'Unknown'
      };

      // Try to get more detailed location info if we don't have it
      if (!selectedMapLocation.city || selectedMapLocation.city === 'Custom Location') {
        try {
          locationInfo = await reverseGeocodeLocation(
            selectedMapLocation.latitude,
            selectedMapLocation.longitude
          );
        } catch (error) {
          console.log('Using default location info');
        }
      }

      const success = await updateCustomLocationWithCoords(
        locationInfo.city,
        locationInfo.country,
        selectedMapLocation.latitude,
        selectedMapLocation.longitude
      );

      if (success) {
        setShowMapModal(false);
        setSelectedMapLocation(null);
      }
    } catch (error) {
      console.error('Error confirming coordinate location:', error);
      Alert.alert('Error', 'Failed to set location');
    } finally {
      setIsLoadingPremium(false);
    }
  };

  // Render blocked user item
  const renderBlockedUser = ({ item }) => {
    const userId = item;
    const userProfile = blockedUsernames[userId];
    const username = userProfile?.username || 'Unknown User';
    const profileImage = userProfile?.images && userProfile.images.length > 0 ? userProfile.images[0] : null;

    return (
      <View style={settingsStyles.blockedUserItem}>
        <View style={settingsStyles.blockedUserInfo}>
          <View style={settingsStyles.blockedUserAvatar}>
            {profileImage ? (
              <Image
                source={{
                  uri: profileImage.startsWith('data:')
                    ? profileImage
                    : `data:image/jpeg;base64,${profileImage}`
                }}
                style={settingsStyles.blockedUserAvatarImage}
                resizeMode="cover"
              />
            ) : (
              <Text style={settingsStyles.blockedUserAvatarText}>
                {username.charAt(0).toUpperCase()}
              </Text>
            )}
          </View>
          <View style={settingsStyles.blockedUserDetails}>
            <Text style={settingsStyles.blockedUserName}>{username}</Text>
            {userProfile?.age && (
              <Text style={settingsStyles.blockedUserAge}>Age {userProfile.age}</Text>
            )}
          </View>
        </View>
        <TouchableOpacity
          style={settingsStyles.unblockButton}
          onPress={() => handleUnblockUser(userId)}
        >
          <Text style={settingsStyles.unblockButtonText}>Unblock</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Get location status text and color
  const getLocationStatusInfo = () => {
    if (locationPermission === 'granted') {
      return {
        text: 'Location services enabled',
        color: '#e83333',
        icon: 'checkmark-circle'
      };
    } else if (locationPermission === 'denied') {
      return {
        text: 'Location services disabled',
        color: '#ff3b30',
        icon: 'alert-circle'
      };
    } else {
      return {
        text: 'Location permission not determined',
        color: '#ff9500',
        icon: 'help-circle'
      };
    }
  };

  const locationInfo = getLocationStatusInfo();

  return (
    <ScrollView style={[settingsStyles.container, { backgroundColor: themeColors.bgSecondary }]}>
      {/* Location Services Section - Only show if location is not granted */}
      {locationPermission !== 'granted' && (
        <View style={[settingsStyles.section, { backgroundColor: themeColors.bgPrimary }]}>
          <Text style={[settingsStyles.sectionTitle, { color: themeColors.textPrimary }]}>Location Services</Text>

          <View style={settingsStyles.locationStatusContainer}>
            <Ionicons name={locationInfo.icon} size={24} color={locationInfo.color} style={settingsStyles.locationIcon} />
            <Text style={[settingsStyles.locationStatusText, { color: locationInfo.color }]}>
              {locationInfo.text}
            </Text>
          </View>

          <Text style={[settingsStyles.locationDescription, { color: themeColors.textSecondary }]}>
            Shake & Match requires location services to find nearby matches. Without location access, the app cannot function properly.
          </Text>

          <TouchableOpacity
            style={[
              settingsStyles.locationButton,
              { backgroundColor: themeColors.primary }
            ]}
            onPress={requestLocationPermission}
            disabled={isCheckingLocation}
          >
            {isCheckingLocation ? (
              <Text style={[settingsStyles.locationButtonText, { color: '#fff' }]}>Checking...</Text>
            ) : (
              <Text style={[settingsStyles.locationButtonText, { color: '#fff' }]}>Enable Location</Text>
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* Premium Section */}
      <View style={[settingsStyles.section, { backgroundColor: themeColors.bgPrimary }, (premiumStatus && premiumStatus.isPremium) ? settingsStyles.premiumSection : null]}>
        <View style={settingsStyles.premiumHeader}>
          <Text style={[settingsStyles.sectionTitle, { color: themeColors.textPrimary }]}>Premium Features</Text>
          <View style={settingsStyles.premiumHeaderRight}>
            {(premiumStatus && premiumStatus.isPremium) && (
              <View style={settingsStyles.premiumBadge}>
                <Ionicons name="star" size={16} color="#ff9d00ff" />
                <Text style={settingsStyles.premiumBadgeText}>PREMIUM</Text>
              </View>
            )}
            <TouchableOpacity
              style={settingsStyles.refreshButton}
              onPress={loadPremiumStatus}
              disabled={isLoadingPremium}
            >
              <Ionicons
                name="refresh"
                size={20}
                color={isLoadingPremium ? "#ccc" : themeColors.primary}
              />
            </TouchableOpacity>
          </View>
        </View>



        {(premiumStatus && premiumStatus.isPremium) ? (
          <>
            <View style={settingsStyles.premiumInfo}>
              <Text style={[settingsStyles.premiumInfoText, { color: themeColors.textPrimary }]}>
                You have an active premium subscription!
              </Text>
              {(premiumStatus.daysRemaining && premiumStatus.daysRemaining > 0) && (
                <Text style={[settingsStyles.premiumDaysText, { color: themeColors.textSecondary }]}>
                  {premiumStatus.daysRemaining} days remaining
                </Text>
              )}
            </View>

            {/* Custom Location Feature */}
            <View style={settingsStyles.premiumFeature}>
              <View style={settingsStyles.featureHeader}>
                <Ionicons name="location" size={20} color={themeColors.primary} />
                <Text style={[settingsStyles.featureTitle, { color: themeColors.textPrimary }]}>Custom Location</Text>
              </View>

              {(customLocation && customLocation.enabled) ? (
                <View style={settingsStyles.customLocationActive}>
                  <Text style={[settingsStyles.customLocationText, { color: themeColors.textPrimary }]}>
                    Active: {customLocation?.city || 'Unknown'}, {customLocation?.country || 'Unknown'}
                  </Text>
                  <View style={settingsStyles.locationButtons}>
                    <TouchableOpacity
                      style={[settingsStyles.changeLocationButton, { backgroundColor: themeColors.primary }]}
                      onPress={openCoordinateLocationPicker}
                    >
                      <Ionicons name="location" size={16} color="#fff" />
                      <Text style={[settingsStyles.changeLocationButtonText, { color: '#fff' }]}>Change Location</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[settingsStyles.disableLocationButton, { backgroundColor: themeColors.textSecondary }]}
                      onPress={disableCustomLocation}
                      disabled={isLoadingPremium}
                    >
                      <Text style={[settingsStyles.disableLocationButtonText, { color: '#fff' }]}>
                        {isLoadingPremium ? 'Disabling...' : 'Disable'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <View style={settingsStyles.customLocationInactive}>
                  <Text style={[settingsStyles.featureDescription, { color: themeColors.textSecondary }]}>
                    Change your location to appear in different cities and match with users worldwide.
                  </Text>
                  <TouchableOpacity
                    style={[settingsStyles.enableLocationButton, { backgroundColor: themeColors.primary }]}
                    onPress={openCoordinateLocationPicker}
                    disabled={isLoadingPremium}
                  >
                    <Ionicons name="location" size={20} color="#fff" />
                    <Text style={[settingsStyles.enableLocationButtonText, { color: '#fff' }]}>
                      {isLoadingPremium ? 'Loading...' : 'Set Custom Location'}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </>
        ) : (
          <View style={[settingsStyles.premiumUpgrade, { backgroundColor: themeColors.bgPrimary }]}>
            <View style={settingsStyles.authWarning}>
              <Ionicons name="warning" size={24} color="#FF3B30" />
              <Text style={[settingsStyles.authWarningText, { color: themeColors.textPrimary }]}>
                Authentication Required
              </Text>
            </View>
            <Text style={[settingsStyles.authWarningDescription, { color: themeColors.textSecondary }]}>
              You need to log in again to check your premium status and access premium features.
            </Text>
            <TouchableOpacity
              style={[settingsStyles.upgradeButton, { backgroundColor: themeColors.primary }]}
              onPress={() => navigation.navigate('Login')}
            >
              <Ionicons name="log-in" size={20} color="#fff" />
              <Text style={[settingsStyles.upgradeButtonText, { color: '#fff' }]}>Log In Again</Text>
            </TouchableOpacity>

            <View style={settingsStyles.premiumFeaturesList}>
              <Text style={[settingsStyles.upgradeTitle, { color: themeColors.textPrimary }]}>Premium Features Available:</Text>
              <Text style={[settingsStyles.upgradeDescription, { color: themeColors.textSecondary }]}>
                • Change your location to match with users worldwide{'\n'}
                • Priority matching{'\n'}
                • Advanced filters{'\n'}
                • And more!
              </Text>
            </View>
          </View>
        )}
      </View>


      <View style={[settingsStyles.section, { backgroundColor: themeColors.bgPrimary }]}>
        <Text style={[settingsStyles.sectionTitle, { color: themeColors.textPrimary }]}>Match Settings</Text>
        
        <View style={settingsStyles.settingItem}>
          <Text style={[settingsStyles.settingLabel, { color: themeColors.textPrimary }]}>Maximum Match Distance</Text>
          <Text style={[settingsStyles.settingValue, { color: themeColors.textSecondary }]}>{Math.round(distance)} km</Text>
        </View>
        
        <Slider
          style={settingsStyles.slider}
          minimumValue={1}
          maximumValue={50}
          step={1}
          value={distance}
          onValueChange={handleDistanceChange}
          minimumTrackTintColor={themeColors.primary}
          maximumTrackTintColor="#d3d3d3"
          thumbTintColor={themeColors.primary}
        />
        
        <View style={settingsStyles.sliderLabels}>
          <Text style={[settingsStyles.sliderLabel, { color: themeColors.textSecondary }]}>1 km</Text>
          <Text style={[settingsStyles.sliderLabel, { color: themeColors.textSecondary }]}>50 km</Text>
        </View>
        
        <View style={settingsStyles.divider} />
        
        {/* Minimum Age Setting */}
        <View style={settingsStyles.settingItem}>
          <Text style={[settingsStyles.settingLabel, { color: themeColors.textPrimary }]}>Minimum Age</Text>
          <Text style={[settingsStyles.settingValue, { color: themeColors.textSecondary }]}>{minimumAge} years</Text>
        </View>
        
        <Slider
          style={settingsStyles.slider}
          minimumValue={18}
          maximumValue={75}
          step={1}
          value={minimumAge}
          onValueChange={handleMinAgeChange}
          minimumTrackTintColor={themeColors.primary}
          maximumTrackTintColor="#d3d3d3"
          thumbTintColor={themeColors.primary}
        />
        
        <View style={settingsStyles.sliderLabels}>
          <Text style={[settingsStyles.sliderLabel, { color: themeColors.textSecondary }]}>18 yrs</Text>
          <Text style={[settingsStyles.sliderLabel, { color: themeColors.textSecondary }]}>75 yrs</Text>
        </View>
        
        {/* Maximum Age Setting */}
        <View style={settingsStyles.settingItem}>
          <Text style={[settingsStyles.settingLabel, { color: themeColors.textPrimary }]}>Maximum Age</Text>
          <Text style={[settingsStyles.settingValue, { color: themeColors.textSecondary }]}>{maximumAge} years</Text>
        </View>
        
        <Slider
          style={settingsStyles.slider}
          minimumValue={19}
          maximumValue={99}
          step={1}
          value={maximumAge}
          onValueChange={handleMaxAgeChange}
          minimumTrackTintColor={themeColors.primary}
          maximumTrackTintColor="#d3d3d3"
          thumbTintColor={themeColors.primary}
        />
        
        <View style={settingsStyles.sliderLabels}>
          <Text style={[settingsStyles.sliderLabel, { color: themeColors.textSecondary }]}>19 yrs</Text>
          <Text style={[settingsStyles.sliderLabel, { color: themeColors.textSecondary }]}>99 yrs</Text>
        </View>

        <Text style={[settingsStyles.settingDescription, { color: themeColors.textSecondary }]}>
          You will only be matched with users who are between {minimumAge} and {maximumAge} years old.
        </Text>
      </View>

      {/* Gender and Matching Preferences Section */}
      <View style={[settingsStyles.section, { backgroundColor: themeColors.bgPrimary }]}>
        <Text style={[settingsStyles.sectionTitle, { color: themeColors.textPrimary }]}>Profile & Matching</Text>

        {/* User Gender Selection */}
        <View style={settingsStyles.settingItem}>
          <Text style={[settingsStyles.settingLabel, { color: themeColors.textPrimary }]}>My Gender</Text>
        </View>
        <View style={settingsStyles.genderContainer}>
          <TouchableOpacity
            style={[
              settingsStyles.genderButton,
              { backgroundColor: themeColors.bgSecondary, borderColor: themeColors.border },
              userGender === 'male' && { backgroundColor: themeColors.primary }
            ]}
            onPress={() => {
              setUserGender('male');
              saveUserPreferences('male', interestedIn, lookingFor);
            }}
          >
            <Ionicons
              name="man"
              size={20}
              color={userGender === 'male' ? '#fff' : themeColors.primary}
            />
            <Text style={[
              settingsStyles.genderButtonText,
              { color: userGender === 'male' ? '#fff' : themeColors.textPrimary }
            ]}>
              Male
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              settingsStyles.genderButton,
              { backgroundColor: themeColors.bgSecondary, borderColor: themeColors.border },
              userGender === 'female' && { backgroundColor: themeColors.primary }
            ]}
            onPress={() => {
              setUserGender('female');
              saveUserPreferences('female', interestedIn, lookingFor);
            }}
          >
            <Ionicons
              name="woman"
              size={20}
              color={userGender === 'female' ? '#fff' : themeColors.primary}
            />
            <Text style={[
              settingsStyles.genderButtonText,
              { color: userGender === 'female' ? '#fff' : themeColors.textPrimary }
            ]}>
              Female
            </Text>
          </TouchableOpacity>
        </View>

        <View style={settingsStyles.divider} />

        {/* Interested In Selection */}
        <View style={settingsStyles.settingItem}>
          <Text style={[settingsStyles.settingLabel, { color: themeColors.textPrimary }]}>Interested In</Text>
        </View>
        <View style={settingsStyles.genderContainer}>
          <TouchableOpacity
            style={[
              settingsStyles.genderButton,
              { backgroundColor: themeColors.bgSecondary, borderColor: themeColors.border },
              interestedIn === 'male' && { backgroundColor: themeColors.primary }
            ]}
            onPress={() => {
              setInterestedIn('male');
              saveUserPreferences(userGender, 'male', lookingFor);
            }}
          >
            <Ionicons
              name="man"
              size={20}
              color={interestedIn === 'male' ? '#fff' : themeColors.primary}
            />
            <Text style={[
              settingsStyles.genderButtonText,
              { color: interestedIn === 'male' ? '#fff' : themeColors.textPrimary }
            ]}>
              Male
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              settingsStyles.genderButton,
              { backgroundColor: themeColors.bgSecondary, borderColor: themeColors.border },
              interestedIn === 'female' && { backgroundColor: themeColors.primary }
            ]}
            onPress={() => {
              setInterestedIn('female');
              saveUserPreferences(userGender, 'female', lookingFor);
            }}
          >
            <Ionicons
              name="woman"
              size={20}
              color={interestedIn === 'female' ? '#fff' : themeColors.primary}
            />
            <Text style={[
              settingsStyles.genderButtonText,
              { color: interestedIn === 'female' ? '#fff' : themeColors.textPrimary }
            ]}>
              Female
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              settingsStyles.genderButton,
              { backgroundColor: themeColors.bgSecondary, borderColor: themeColors.border },
              interestedIn === 'both' && { backgroundColor: themeColors.primary }
            ]}
            onPress={() => {
              setInterestedIn('both');
              saveUserPreferences(userGender, 'both', lookingFor);
            }}
          >
            <Ionicons
              name="people"
              size={20}
              color={interestedIn === 'both' ? '#fff' : themeColors.primary}
            />
            <Text style={[
              settingsStyles.genderButtonText,
              { color: interestedIn === 'both' ? '#fff' : themeColors.textPrimary }
            ]}>
              Both
            </Text>
          </TouchableOpacity>
        </View>

        <View style={settingsStyles.divider} />

        {/* Looking For Toggle */}
        <View style={settingsStyles.settingToggle}>
          <View style={settingsStyles.toggleInfo}>
            <Text style={[settingsStyles.settingLabel, { color: themeColors.textPrimary }]}>Looking For</Text>
            <Text style={[settingsStyles.toggleDescription, { color: themeColors.textSecondary }]}>
              {lookingFor === 'relationship' ? 'Relationship' : 'Friends'}
            </Text>
          </View>
          <View style={settingsStyles.customToggle}>
            <TouchableOpacity
              style={[
                settingsStyles.toggleOption,
                { backgroundColor: themeColors.bgSecondary, borderColor: themeColors.border },
                lookingFor === 'relationship' && { backgroundColor: themeColors.primary }
              ]}
              onPress={() => {
                setLookingFor('relationship');
                saveUserPreferences(userGender, interestedIn, 'relationship');
              }}
            >
              <Ionicons
                name="heart"
                size={16}
                color={lookingFor === 'relationship' ? '#fff' : themeColors.primary}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                settingsStyles.toggleOption,
                { backgroundColor: themeColors.bgSecondary, borderColor: themeColors.border },
                lookingFor === 'friends' && { backgroundColor: themeColors.primary }
              ]}
              onPress={() => {
                setLookingFor('friends');
                saveUserPreferences(userGender, interestedIn, 'friends');
              }}
            >
              <Ionicons
                name="people"
                size={16}
                color={lookingFor === 'friends' ? '#fff' : themeColors.primary}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      
      <View style={[settingsStyles.section, { backgroundColor: themeColors.bgPrimary }]}>
        <Text style={[settingsStyles.sectionTitle, { color: themeColors.textPrimary }]}>Notifications</Text>

        <View style={settingsStyles.settingToggle}>
          <Text style={[settingsStyles.settingLabel, { color: themeColors.textPrimary }]}>Enable Notifications</Text>
          <Switch
            value={notificationsEnabled}
            onValueChange={setNotificationsEnabled}
            trackColor={{ false: '#d3d3d3', true: themeColors.primary }}
            thumbColor="#fff"
          />
        </View>
      </View>

      {/* Theme Selection Section */}
      <View style={[settingsStyles.section, { backgroundColor: themeColors.bgPrimary }]}>
        <Text style={[settingsStyles.sectionTitle, { color: themeColors.textPrimary }]}>Theme</Text>

        {availableThemes.map((themeName) => (
          <TouchableOpacity
            key={themeName}
            style={[
              settingsStyles.themeOption,
              { backgroundColor: themeColors.bgSecondary, borderColor: themeColors.border },
              currentTheme === themeName && { backgroundColor: themeColors.primaryLight, borderColor: themeColors.primary }
            ]}
            onPress={() => changeTheme(themeName)}
          >
            <View style={settingsStyles.themeOptionContent}>
              <Text style={[
                settingsStyles.themeOptionText,
                { color: themeColors.textPrimary },
                currentTheme === themeName && { color: themeColors.primary, fontWeight: '600' }
              ]}>
                {getThemeDisplayName(themeName)}
              </Text>
              {currentTheme === themeName && (
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color={themeColors.primary}
                />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <View style={[settingsStyles.section, { backgroundColor: themeColors.bgPrimary }]}>
        <Text style={[settingsStyles.sectionTitle, { color: themeColors.textPrimary }]}>Blocked Users</Text>

        {blockedUsers.length === 0 ? (
          <Text style={[settingsStyles.noBlockedText, { color: themeColors.textSecondary }]}>You haven't blocked any users.</Text>
        ) : (
          <FlatList
            data={blockedUsers}
            keyExtractor={(item) => item}
            renderItem={renderBlockedUser}
            scrollEnabled={false}
          />
        )}
      </View>
      
      <View style={[settingsStyles.section, { backgroundColor: themeColors.bgPrimary }]}>
        <TouchableOpacity
          style={[settingsStyles.logoutButton, { backgroundColor: themeColors.primary }]}
          onPress={handleLogout}
        >
          <Ionicons name="log-out-outline" size={24} color="#fff" />
          <Text style={settingsStyles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </View>

      {/* Custom Location Modal */}
      <Modal
        visible={showLocationModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowLocationModal(false)}
      >
        <View style={settingsStyles.modalContainer}>
          <View style={settingsStyles.modalHeader}>
            <TouchableOpacity
              style={settingsStyles.modalCloseButton}
              onPress={() => setShowLocationModal(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
            <Text style={[settingsStyles.modalTitle, { color: themeColors.textPrimary }]}>Set Custom Location</Text>
            <View style={settingsStyles.modalHeaderSpacer} />
          </View>

          <View style={settingsStyles.modalContent}>
            <Text style={[settingsStyles.modalDescription, { color: themeColors.textSecondary }]}>
              Choose a city where you'd like to appear for matching. This is a premium feature that lets you connect with users worldwide.
            </Text>

            <View style={settingsStyles.inputGroup}>
              <Text style={[settingsStyles.inputLabel, { color: themeColors.textPrimary }]}>City</Text>
              <TextInput
                style={settingsStyles.textInput}
                value={newCity}
                onChangeText={setNewCity}
                placeholder="Enter city name"
                placeholderTextColor="#999"
              />
            </View>

            <View style={settingsStyles.inputGroup}>
              <Text style={[settingsStyles.inputLabel, { color: themeColors.textPrimary }]}>Country</Text>
              <TextInput
                style={settingsStyles.textInput}
                value={newCountry}
                onChangeText={setNewCountry}
                placeholder="Enter country name"
                placeholderTextColor="#999"
              />
            </View>

            <TouchableOpacity
              style={[settingsStyles.saveLocationButton, { opacity: isLoadingPremium ? 0.6 : 1 }]}
              onPress={updateCustomLocation}
              disabled={isLoadingPremium}
            >
              <Text style={[settingsStyles.saveLocationButtonText, { color: '#fff' }]}>
                {isLoadingPremium ? 'Saving...' : 'Save Location'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Interactive Map Modal */}
      <Modal
        visible={showMapModal}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowMapModal(false)}
      >
        <View style={settingsStyles.mapModalContainer}>
          <View style={settingsStyles.mapModalHeader}>
            <TouchableOpacity
              style={settingsStyles.modalCloseButton}
              onPress={() => setShowMapModal(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
            <Text style={[settingsStyles.modalTitle, { color: themeColors.textPrimary }]}>Choose Your Location</Text>
            <TouchableOpacity
              style={[settingsStyles.confirmMapButton, { opacity: selectedMapLocation ? 1 : 0.5 }]}
              onPress={confirmCoordinateLocation}
              disabled={!selectedMapLocation || isLoadingPremium}
            >
              <Text style={[settingsStyles.confirmMapButtonText, { color: '#fff' }]}>
                {isLoadingPremium ? 'Setting...' : 'Confirm'}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={settingsStyles.interactiveMapContainer}>
            <WebView
              style={settingsStyles.mapWebView}
              source={{ html: getGoogleMapsHTML() }}
              onMessage={handleMapMessage}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              startInLoadingState={true}
              renderLoading={() => (
                <View style={settingsStyles.mapLoadingContainer}>
                  <Text style={settingsStyles.mapLoadingText}>Loading Google Maps...</Text>
                </View>
              )}
            />
          </View>

          <View style={settingsStyles.mapFooter}>
            {selectedMapLocation ? (
              <Text style={settingsStyles.selectedLocationText}>
                📍 Selected: {selectedMapLocation.city || 'Custom Location'}
                {'\n'}Coordinates: {selectedMapLocation.latitude.toFixed(4)}, {selectedMapLocation.longitude.toFixed(4)}
              </Text>
            ) : (
              <Text style={settingsStyles.mapInstructionsText}>
                Tap on a continent or anywhere on the map to select your location
              </Text>
            )}
          </View>
        </View>
      </Modal>


    </ScrollView>
  );
};

// Styles are now imported from ScreensStyles.js

export default SettingsScreen;

