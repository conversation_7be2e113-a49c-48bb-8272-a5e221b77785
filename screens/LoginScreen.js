// screens/LoginScreen.js - Modern design with fixed keyboard issues
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Image,
  Platform,
  Keyboard,
  Dimensions,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { loginStyles, theme } from './ScreensStyles';
import { useThemedStyles, useThemeColors } from '../contexts/ThemeContext';
import SocialAuthService from '../services/SocialAuthService';

const LoginScreen = ({ navigation, onLogin, serverAddress: propServerAddress }) => {
  // Force re-render when theme changes
  useThemedStyles();
  // Get current theme colors
  const themeColors = useThemeColors();

  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [socialLoading, setSocialLoading] = useState(null); // Track which social login is loading
  const [serverAddress, setServerAddress] = useState(propServerAddress || '**************:3000');
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [screenHeight, setScreenHeight] = useState(Dimensions.get('window').height);
  const [availableProviders, setAvailableProviders] = useState([]);
  
  // Animation values
  const logoPosition = useRef(new Animated.Value(0)).current;
  const formOpacity = useRef(new Animated.Value(1)).current;
  
  // Reference for password input
  const passwordInputRef = useRef(null);

  // Handle keyboard appearance and animations
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event) => {
        setKeyboardVisible(true);
        // Calculate how much we need to move the form up to avoid keyboard
        const keyboardHeight = event.endCoordinates.height;
        // Calculate screen space and adjust movement accordingly
        const availableSpace = screenHeight - keyboardHeight;
        // Move the entire container up enough to ensure form is visible
        // On iOS we need to consider the keyboard takes up more space
        const formHeight = 300; // Estimated form height
        const contentHeight = 450; // Estimated total content height
        const availableHeight = screenHeight - keyboardHeight;
        
        // Calculate how much we need to move to ensure fields are visible
        const moveUpValue = Platform.OS === 'ios' 
          ? -Math.min(contentHeight - availableHeight + 200, 250) 
          : -Math.min(keyboardHeight * 0.5, 150);
        
        Animated.parallel([
          Animated.timing(logoPosition, {
            toValue: moveUpValue,
            duration: 300,
            useNativeDriver: true
          }),
          Animated.timing(formOpacity, {
            toValue: 0.97,
            duration: 300,
            useNativeDriver: true
          })
        ]).start();
      }
    );
    
    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        Animated.parallel([
          Animated.timing(logoPosition, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true
          }),
          Animated.timing(formOpacity, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true
          })
        ]).start();
      }
    );

    // Screen dimensions listener for orientation changes
    const dimensionListener = Dimensions.addEventListener(
      'change',
      ({ window }) => {
        setScreenHeight(window.height);
      }
    );

    // Cleanup
    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
      dimensionListener.remove();
    };
  }, []);

  // Load available social providers
  useEffect(() => {
    const loadProviders = async () => {
      try {
        const providers = await SocialAuthService.getAvailableProviders();
        setAvailableProviders(providers);
      } catch (error) {
        console.error('Error loading social providers:', error);
      }
    };
    loadProviders();
  }, []);

  // Social login handler
  const handleSocialLogin = async (provider) => {
    setSocialLoading(provider);

    try {
      let socialUserData;

      switch (provider) {
        case 'google':
          socialUserData = await SocialAuthService.signInWithGoogle();
          break;
        case 'apple':
          socialUserData = await SocialAuthService.signInWithApple();
          break;
        case 'facebook':
          socialUserData = await SocialAuthService.signInWithFacebook();
          break;
        default:
          throw new Error('Unsupported provider');
      }

      // Create or authenticate user with your backend
      await handleSocialAuth(socialUserData);

    } catch (error) {
      console.error(`${provider} login error:`, error);
      Alert.alert(
        'Login Failed',
        error.message || `${provider} sign-in failed. Please try again.`
      );
    } finally {
      setSocialLoading(null);
    }
  };

  // Handle social authentication with backend
  const handleSocialAuth = async (socialUserData) => {
    try {
      await AsyncStorage.setItem('serverAddress', serverAddress);

      // Send social login data to your backend
      const response = await fetch(`http://${serverAddress}/api/social-login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: socialUserData.provider,
          providerId: socialUserData.id,
          email: socialUserData.email,
          name: socialUserData.name,
          photo: socialUserData.photo,
          token: socialUserData.token,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Social login failed');
      }

      // Create user data object for the app (store token if provided)
      const userData = {
        id: data.userId,
        username: data.username || socialUserData.name || socialUserData.email,
        token: data.token || null,
      };

      console.log('Social login successful, user data:', JSON.stringify(userData));

      // Store user data
      await AsyncStorage.setItem('user', JSON.stringify(userData));

      // Call the onLogin callback
      onLogin(userData, serverAddress);

    } catch (error) {
      console.error('Social auth backend error:', error);
      throw error;
    }
  };

  const handleLogin = async () => {
    Keyboard.dismiss();
    
    // Validation
    if (!username.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter username and password');
      return;
    }

    setIsLoading(true);

    try {
      await AsyncStorage.setItem('serverAddress', serverAddress);
      console.log(`Attempting login for ${username} to http://${serverAddress}/api/login`);
      
      // Log the exact request being sent
      const requestBody = JSON.stringify({
        username,
        password,
      });
      console.log('Request payload:', requestBody);
      
      try {
        // First attempt with more detailed error handling
        const response = await fetch(`http://${serverAddress}/api/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: requestBody,
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', JSON.stringify(response.headers));
        
        // Get the raw text first
        const responseText = await response.text();
        console.log('Raw response text:', responseText);
        
        // Check if response is empty
        if (!responseText) {
          console.error('Empty response received from server');
          throw new Error('Server returned an empty response');
        }
        
        // Try to parse as JSON
        let data;
        try {
          data = JSON.parse(responseText);
          console.log('Parsed response data:', JSON.stringify(data));
        } catch (parseError) {
          console.error('Error parsing response as JSON:', parseError);
          throw new Error('Server returned invalid JSON: ' + responseText.substring(0, 100));
        }

        if (!response.ok) {
          throw new Error(data.message || 'Login failed');
        }

        // Extract userId with detailed logging
        console.log('Looking for userId in response...');
        const userId = data.userId || (data.user && data.user.id);
        console.log('Extracted userId:', userId);
        
        if (!userId) {
          console.error('User ID missing from response:', JSON.stringify(data));
          throw new Error('User ID missing from server response');
        }

        // Create user data object (store token if provided)
        const userData = {
          id: userId,
          username: username,
          token: data.token || null,
        };

        console.log('Login successful, user data:', JSON.stringify(userData));
        
        // Test storing in AsyncStorage before calling onLogin
        try {
          await AsyncStorage.setItem('test_user', JSON.stringify(userData));
          const testRead = await AsyncStorage.getItem('test_user');
          console.log('Test AsyncStorage read:', testRead);
        } catch (storageError) {
          console.error('AsyncStorage test failed:', storageError);
        }
        
        // Call the onLogin function
        console.log('Calling onLogin with userData');
        onLogin(userData, serverAddress);
      } catch (fetchError) {
        console.error('Fetch operation error:', fetchError);
        // Try an alternative approach with XMLHttpRequest for diagnostic purposes
        console.log('Attempting fallback XMLHttpRequest...');
        
        const xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function() {
          if (xhr.readyState === 4) {
            console.log('XHR Status:', xhr.status);
            console.log('XHR Response:', xhr.responseText);
            
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const xhrData = JSON.parse(xhr.responseText);
                console.log('XHR Parsed data:', xhrData);
                
                const xhrUserId = xhrData.userId || (xhrData.user && xhrData.user.id);
                if (xhrUserId) {
                  const xhrUserData = {
                    id: xhrUserId,
                    username: username,
                    token: xhrData.token || null,
                  };
                  console.log('XHR Login successful:', xhrUserData);
                  onLogin(xhrUserData, serverAddress);
                } else {
                  throw new Error('User ID missing from XHR response');
                }
              } catch (e) {
                console.error('XHR parse error:', e);
                throw new Error('Failed to process XHR response');
              }
            } else {
              throw new Error(`XHR failed with status ${xhr.status}`);
            }
          }
        };
        
        xhr.open('POST', `http://${serverAddress}/api/login`, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('Accept', 'application/json');
        xhr.send(requestBody);
        
        throw fetchError; // Still throw the original error
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert(
        'Login Failed',
        `${error.message || 'Invalid username or password. Please try again.'}`
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={loginStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={theme.colors.primary} />

      <Animated.View
        style={[
          loginStyles.container,
          { transform: [{ translateY: logoPosition }], backgroundColor: themeColors.bgSecondary }
        ]}
      >
        {/* Background gradient */}
        <View style={loginStyles.backgroundGradient} />

        {/* Floating shapes - purely decorative */}
        <View style={[loginStyles.floatingShape, loginStyles.floatingShape1]} />
        <View style={[loginStyles.floatingShape, loginStyles.floatingShape2]} />
        <View style={[loginStyles.floatingShape, loginStyles.floatingShape3]} />

        {/* Logo section */}
        <View style={loginStyles.logoContainer}>
          <Image
            source={require('../assets/logo.png')}
            style={loginStyles.logo}
            resizeMode="contain"
            fadeDuration={0}
          />
          <Text style={[loginStyles.appTitle, { color: themeColors.textPrimary }]}>Shake & Match</Text>
          <Text style={[loginStyles.appSubtitle, { color: themeColors.textSecondary }]}>Sign in to your account</Text>
        </View>

        {/* Form section with animation */}
        <Animated.View
          style={[
            loginStyles.formContainer,
            {
              opacity: formOpacity,
              transform: [
                {
                  scale: formOpacity.interpolate({
                    inputRange: [0.97, 1],
                    outputRange: [0.98, 1]
                  })
                }
              ]
            }
          ]}
        >
          {/* Username input */}
          <View style={loginStyles.inputContainer}>
            <TextInput
              style={loginStyles.input}
              placeholder="Username"
              placeholderTextColor="#8a9cb0"
              value={username}
              onChangeText={setUsername}
              autoCapitalize="none"
              autoCorrect={false}
              spellCheck={false}
              returnKeyType="next"
              onSubmitEditing={() => passwordInputRef.current?.focus()}
              textContentType="username"
              autoComplete="username"
            />
          </View>

          {/* Password input */}
          <View style={loginStyles.inputContainer}>
            <TextInput
              ref={passwordInputRef}
              style={loginStyles.input}
              placeholder="Password"
              placeholderTextColor="#8a9cb0"
              value={password}
              onChangeText={setPassword}
              secureTextEntry={true}
              autoCapitalize="none"
              autoCorrect={false}
              spellCheck={false}
              returnKeyType="done"
              onSubmitEditing={handleLogin}
              textContentType="password"
              autoComplete="password"
              importantForAutofill="yes"
              passwordRules="minlength: 6;"
            />
          </View>

          <TouchableOpacity
            style={loginStyles.loginButton}
            onPress={handleLogin}
            disabled={isLoading}
            activeOpacity={0.85}
          >
            {isLoading ? (
              <ActivityIndicator color={theme.colors.textWhite} />
            ) : (
              <>
                <Text style={loginStyles.loginButtonText}>Sign In</Text>
                <Ionicons name="arrow-forward" size={20} color={theme.colors.textWhite} />
              </>
            )}
          </TouchableOpacity>

          {/* Social Login Buttons */}
          <View style={loginStyles.socialButtonsContainer}>
            <View style={loginStyles.orDivider}>
              <View style={loginStyles.orLine} />
              <Text style={loginStyles.orText}>Or continue with</Text>
              <View style={loginStyles.orLine} />
            </View>

            {availableProviders.includes('google') && (
              <TouchableOpacity
                style={[loginStyles.socialButton, { backgroundColor: '#db4437' }]}
                onPress={() => handleSocialLogin('google')}
                disabled={socialLoading !== null}
                activeOpacity={0.8}
              >
                {socialLoading === 'google' ? (
                  <ActivityIndicator color={theme.colors.textWhite} size="small" />
                ) : (
                  <>
                    <Ionicons name="logo-google" size={20} color={theme.colors.textWhite} />
                    <Text style={loginStyles.socialButtonText}>Google</Text>
                  </>
                )}
              </TouchableOpacity>
            )}

            {availableProviders.includes('apple') && (
              <TouchableOpacity
                style={[loginStyles.socialButton, { backgroundColor: '#000' }]}
                onPress={() => handleSocialLogin('apple')}
                disabled={socialLoading !== null}
                activeOpacity={0.8}
              >
                {socialLoading === 'apple' ? (
                  <ActivityIndicator color={theme.colors.textWhite} size="small" />
                ) : (
                  <>
                    <Ionicons name="logo-apple" size={20} color={theme.colors.textWhite} />
                    <Text style={loginStyles.socialButtonText}>Apple</Text>
                  </>
                )}
              </TouchableOpacity>
            )}

            {availableProviders.includes('facebook') && (
              <TouchableOpacity
                style={[loginStyles.socialButton, { backgroundColor: '#4267B2' }]}
                onPress={() => handleSocialLogin('facebook')}
                disabled={socialLoading !== null}
                activeOpacity={0.8}
              >
                {socialLoading === 'facebook' ? (
                  <ActivityIndicator color={theme.colors.textWhite} size="small" />
                ) : (
                  <>
                    <Ionicons name="logo-facebook" size={20} color={theme.colors.textWhite} />
                    <Text style={loginStyles.socialButtonText}>Facebook</Text>
                  </>
                )}
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>
      </Animated.View>
    </SafeAreaView>
  );
};

// Styles are now imported from ScreensStyles.js

export default LoginScreen;