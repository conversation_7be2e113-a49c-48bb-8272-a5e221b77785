// contexts/ThemeContext.js
import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { setCurrentTheme } from '../screens/ScreensStyles';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Custom hook for components that need to re-render when theme changes
export const useThemedStyles = () => {
  const { forceUpdate, currentTheme } = useTheme();
  // This hook will cause components to re-render when forceUpdate changes
  return { forceUpdate, currentTheme };
};

// Hook to get current theme colors
export const useThemeColors = () => {
  const { currentTheme, forceUpdate } = useTheme();

  const themes = {
    default: {
      primary: '#e83333',
      primaryDark: '#d12929',
      primaryLight: '#ffe5e5',
      secondary: '#f8f9fa',
      accent: '#ff6b6b',
      textPrimary: '#2c3e50',
      textSecondary: '#7f8c8d',
      textLight: '#bdc3c7',
      textWhite: '#ffffff',
      textError: '#ff3b30',
      textSuccess: '#28a745',
      bgPrimary: '#ffffff',
      bgSecondary: '#f8f8f8',
      bgTertiary: '#e9ecef',
      bgError: '#fff5f5',
      bgSuccess: '#f0fff4',
      borderPrimary: '#dee2e6',
      borderSecondary: '#e0e0e0',
      borderError: '#ff3b30',
      borderSuccess: '#28a745',
      gold: '#FFD700',
      blue: '#4e9af1',
      blueDark: '#3a7bc8',
      overlayDark: 'rgba(0, 0, 0, 0.5)',
      overlayLight: 'rgba(255, 255, 255, 0.9)',
      shadowColor: '#000000',
    },
    dark: {
      primary: '#e83333',
      primaryDark: '#d12929',
      primaryLight: '#2a1a1a',
      secondary: '#2c2c2c',
      accent: '#ff6b6b',
      textPrimary: '#ffffff',
      textSecondary: '#b0b0b0',
      textLight: '#808080',
      textWhite: '#ffffff',
      textError: '#ff3b30',
      textSuccess: '#28a745',
      bgPrimary: '#1a1a1a',
      bgSecondary: '#2c2c2c',
      bgTertiary: '#3a3a3a',
      bgError: '#2a1a1a',
      bgSuccess: '#1a2a1a',
      borderPrimary: '#404040',
      borderSecondary: '#505050',
      borderError: '#ff3b30',
      borderSuccess: '#28a745',
      gold: '#FFD700',
      blue: '#4e9af1',
      blueDark: '#3a7bc8',
      overlayDark: 'rgba(0, 0, 0, 0.8)',
      overlayLight: 'rgba(0, 0, 0, 0.6)',
      shadowColor: '#000000',
    },
    blueish: {
      primary: '#4e9af1',
      primaryDark: '#3a7bc8',
      primaryLight: '#e5f2ff',
      secondary: '#f8f9fa',
      accent: '#6bb6ff',
      textPrimary: '#2c3e50',
      textSecondary: '#7f8c8d',
      textLight: '#bdc3c7',
      textWhite: '#ffffff',
      textError: '#ff3b30',
      textSuccess: '#28a745',
      bgPrimary: '#ffffff',
      bgSecondary: '#f8f9fa',
      bgTertiary: '#e9ecef',
      bgError: '#fff5f5',
      bgSuccess: '#f0fff4',
      borderPrimary: '#dee2e6',
      borderSecondary: '#e0e0e0',
      borderError: '#ff3b30',
      borderSuccess: '#28a745',
      gold: '#FFD700',
      blue: '#4e9af1',
      blueDark: '#3a7bc8',
      overlayDark: 'rgba(0, 0, 0, 0.5)',
      overlayLight: 'rgba(255, 255, 255, 0.9)',
      shadowColor: '#000000',
    },
  };

  return themes[currentTheme] || themes.default;
};

// Hook to get dynamic styles that update with theme changes
export const useDynamicStyles = () => {
  const themeColors = useThemeColors();

  return {
    // Container styles
    container: {
      flex: 1,
      backgroundColor: themeColors.bgPrimary,
    },
    safeArea: {
      flex: 1,
      backgroundColor: themeColors.primary,
    },
    // Button styles
    primaryButton: {
      backgroundColor: themeColors.primary,
      paddingVertical: 15,
      paddingHorizontal: 24,
      borderRadius: 8,
      alignItems: 'center',
    },
    primaryButtonText: {
      color: themeColors.textWhite,
      fontSize: 16,
      fontWeight: '600',
    },
    secondaryButton: {
      borderWidth: 1,
      borderColor: themeColors.primary,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
      alignItems: 'center',
    },
    secondaryButtonText: {
      color: themeColors.primary,
      fontSize: 16,
      fontWeight: '600',
    },
    // Text styles
    textPrimary: {
      color: themeColors.textPrimary,
      fontSize: 16,
    },
    textSecondary: {
      color: themeColors.textSecondary,
      fontSize: 14,
    },
    // Input styles
    input: {
      backgroundColor: themeColors.bgSecondary,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: themeColors.borderSecondary,
      padding: 12,
      fontSize: 16,
      color: themeColors.textPrimary,
    },
    // Card styles
    card: {
      backgroundColor: themeColors.bgPrimary,
      borderRadius: 12,
      padding: 20,
      shadowColor: themeColors.shadowColor,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
  };
};

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentThemeState] = useState('default');
  const [isLoading, setIsLoading] = useState(true);
  const [forceUpdate, setForceUpdate] = useState(0);

  // Load saved theme on app start
  useEffect(() => {
    loadSavedTheme();
  }, []);

  const loadSavedTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('selectedTheme');
      if (savedTheme && ['default', 'dark', 'blueish'].includes(savedTheme)) {
        setCurrentThemeState(savedTheme);
        setCurrentTheme(savedTheme);
      }
    } catch (error) {
      console.error('Error loading saved theme:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const changeTheme = async (themeName) => {
    if (['default', 'dark', 'blueish'].includes(themeName)) {
      try {
        setCurrentThemeState(themeName);
        setCurrentTheme(themeName);
        await AsyncStorage.setItem('selectedTheme', themeName);
        // Force re-render of all components using the theme
        setForceUpdate(prev => prev + 1);
      } catch (error) {
        console.error('Error saving theme:', error);
      }
    }
  };

  const getThemeDisplayName = (themeName) => {
    switch (themeName) {
      case 'default':
        return 'Default';
      case 'dark':
        return 'Dark with Red';
      case 'blueish':
        return 'Blueish';
      default:
        return 'Default';
    }
  };

  const value = {
    currentTheme,
    changeTheme,
    getThemeDisplayName,
    isLoading,
    availableThemes: ['default', 'dark', 'blueish'],
    forceUpdate // This will cause components to re-render when theme changes
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
