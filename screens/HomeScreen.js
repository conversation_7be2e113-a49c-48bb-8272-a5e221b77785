// screens/HomeScreen.js - Fixed with improved shake handling
import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,

  TouchableOpacity,
  FlatList,
  Animated,
  Easing,
  Alert,
  Dimensions,
  Image,
  SafeAreaView,
  StatusBar,
  Linking
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Swipeable } from 'react-native-gesture-handler';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import { homeStyles, theme } from './ScreensStyles';
import { useThemedStyles, useThemeColors } from '../contexts/ThemeContext';

const { width, height } = Dimensions.get('window');

const HomeScreen = ({ navigation, user, userProfile: propUserProfile, shakeDetected, matches, onShake, onDeleteMatch, onUnmatch, onBlockUser, serverAddress }) => {
  // Force re-render when theme changes
  useThemedStyles();
  // Get current theme colors
  const themeColors = useThemeColors();

  // Create dynamic styles based on current theme
  const dynamicStyles = {
    shakeButton: {
      backgroundColor: themeColors.primary,
    },
  };

  // Animation value for shake indicator
  const shakeAnim = React.useRef(new Animated.Value(0)).current;
 // Animation value for shake indicator


  // Floating shapes animation values - more shapes for richer background
  const floatingAnimations = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),

    new Animated.Value(0)
  ]).current;







  // Floating shapes continuous wobble animation - smooth and consistent
  useEffect(() => {
    const startFloatingAnimations = () => {
      floatingAnimations.forEach((anim, index) => {
        // Set consistent starting position
        anim.setValue(0);

        const animateShape = () => {
          Animated.loop(
            Animated.sequence([
              Animated.timing(anim, {
                toValue: 1,
                duration: 6000 + (index % 3) * 1000, // 6-8 seconds, staggered by index
                useNativeDriver: true,
                easing: Easing.inOut(Easing.ease)
              }),
              Animated.timing(anim, {
                toValue: 0,
                duration: 6000 + (index % 3) * 1000, // 6-8 seconds, staggered by index
                useNativeDriver: true,
                easing: Easing.inOut(Easing.ease)
              })
            ]),
            { iterations: -1 } // Infinite loop
          ).start();
        };

        // Start each shape with a staggered delay for natural movement
        setTimeout(() => animateShape(), index * 500);
      });
    };

    startFloatingAnimations();

    return () => {
      floatingAnimations.forEach(anim => anim.stopAnimation());
    };
  }, []);

  // State to track timers for matches
  const [matchTimers, setMatchTimers] = useState({});

  // Premium status state
  const [premiumStatus, setPremiumStatus] = useState(null);
  const [currentLocation, setCurrentLocation] = useState(null);

  // State to store profile data for matches
  const [matchProfiles, setMatchProfiles] = useState({});

  // State to store current user's profile data
  const [userProfile, setUserProfile] = useState(null);

  // Looking for preference state
  const [lookingFor, setLookingFor] = useState('relationship');

  // Timer references to clear intervals
  const timerRefs = useRef({});

  // Manual shake counter for debug
  const [manualShakeCount, setManualShakeCount] = useState(0);
  const [lastManualShake, setLastManualShake] = useState(null);

  // Location permission state
  const [locationPermission, setLocationPermission] = useState(null);
  

  
  // Update animation when shake is detected
  useEffect(() => {
    if (shakeDetected) {
      // Start pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(shakeAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
            easing: Easing.out(Easing.ease),
          }),
          Animated.timing(shakeAnim, {
            toValue: 0.5,
            duration: 500,
            useNativeDriver: true,
            easing: Easing.in(Easing.ease),
          })
        ]),
        { iterations: 4 }
      ).start();
    } else {
      // Reset animation
      shakeAnim.setValue(0);
    }
  }, [shakeDetected]);

  // Fetch current user's profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user?.id || !serverAddress) return;

      try {
        // Check for cached profile first
        const cachedProfile = await AsyncStorage.getItem(`profile_${user.id}`);
        if (cachedProfile) {
          const parsedProfile = JSON.parse(cachedProfile);
          setUserProfile(parsedProfile);
        }

        // Fetch from server to get latest data
        const response = await fetch(`http://${serverAddress}/api/profile/${user.id}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          setUserProfile(data.profile);

          // Cache the profile
          await AsyncStorage.setItem(`profile_${user.id}`, JSON.stringify(data.profile));
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }
    };

    fetchUserProfile();
  }, [user?.id, serverAddress]);

  // Fetch profile data for all matches
  useEffect(() => {
    const fetchMatchProfiles = async () => {
      const profiles = { ...matchProfiles };
      let hasNewProfiles = false;

      for (const match of matches) {
        // Skip if we already have this profile
        if (profiles[match.userId] && profiles[match.userId].images) continue;
        
        try {
          // Check for cached profile first
          const cachedProfile = await AsyncStorage.getItem(`profile_${match.userId}`);
          
          if (cachedProfile) {
            const parsedProfile = JSON.parse(cachedProfile);
            profiles[match.userId] = parsedProfile;
            hasNewProfiles = true;
          } else if (serverAddress) {
            // Fetch from server if not cached
            try {
              const response = await fetch(`http://${serverAddress}/api/profile/${match.userId}`, {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                },
              });
              
              if (response.ok) {
                const data = await response.json();
                profiles[match.userId] = data.profile;
                
                // Cache the profile
                await AsyncStorage.setItem(`profile_${match.userId}`, JSON.stringify(data.profile));
                hasNewProfiles = true;
              }
            } catch (error) {
              console.error(`Error fetching profile for ${match.userId}:`, error);
            }
          }
        } catch (error) {
          console.error('Error handling profile data:', error);
        }
      }
      
      if (hasNewProfiles) {
        setMatchProfiles(profiles);
      }
    };
    
    fetchMatchProfiles();
  }, [matches]);

  // Setup timers for matches
  useEffect(() => {
    // Clear any existing timers
    Object.values(timerRefs.current).forEach(clearInterval);
    timerRefs.current = {};
    const newTimers = {};

    matches.forEach(match => {
      if (match.createdAt && !match.hasActivity) {
        const createdTime = new Date(match.createdAt).getTime();
        const expiryTime = createdTime + 60000; // 1 minute expiry
        
        // Create a timer for this match
        timerRefs.current[match.userId] = setInterval(() => {
          const remainingTime = expiryTime - Date.now();
          
          if (remainingTime <= 0) {
            // Clear the timer if expired
            clearInterval(timerRefs.current[match.userId]);
            delete timerRefs.current[match.userId];
            newTimers[match.userId] = '0s';
          } else {
            // Calculate remaining seconds
            newTimers[match.userId] = `${Math.ceil(remainingTime / 1000)}s`;
          }
          
          setMatchTimers(prev => ({...prev, ...newTimers}));
        }, 1000);
      }
    });

    // Cleanup function
    return () => {
      Object.values(timerRefs.current).forEach(clearInterval);
    };
  }, [matches]);

  useEffect(() => {
    // Update header with profile button on left side and better spacing
    navigation.setOptions({
      headerShown: false, // Hide the default header for a full-screen design
    });
  }, [navigation]);

  // Check location permission and load premium status on component mount
  useEffect(() => {
    checkLocationPermission();
    loadLookingForPreference();
    if (user && serverAddress) {
      loadPremiumStatus();
    }
  }, [user, serverAddress]);

  // Load looking for preference from storage and server
  const loadLookingForPreference = async () => {
    try {
      // First load from local storage
      const savedLookingFor = await AsyncStorage.getItem('lookingFor');
      if (savedLookingFor) {
        setLookingFor(savedLookingFor);
      }

      // Then try to load from server if user is logged in
      if (user && serverAddress) {
        try {
          // Get token from user object, not AsyncStorage
          const token = user.token;

          // Ensure server address has protocol
          const fullServerAddress = serverAddress.startsWith('http') ? serverAddress : `http://${serverAddress}`;

          console.log('📋 Loading preferences from server:', `${fullServerAddress}/api/user/preferences`);
          console.log('🔑 Using token:', token ? 'Token present' : 'No token');

          // First test if server is responding
          try {
            const pingResponse = await fetch(`${fullServerAddress}/api/ping`);
            console.log('🏓 Ping response:', pingResponse.status);
          } catch (pingError) {
            console.log('❌ Server ping failed:', pingError.message);
          }

          const response = await fetch(`${fullServerAddress}/api/user/preferences`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          console.log('📡 Response status:', response.status);

          if (response.ok) {
            const serverPreferences = await response.json();
            console.log('✅ Server preferences loaded:', serverPreferences);
            const serverLookingFor = serverPreferences.lookingFor || 'relationship';

            setLookingFor(serverLookingFor);
            await AsyncStorage.setItem('lookingFor', serverLookingFor);
          } else {
            const errorText = await response.text();
            console.log('❌ Server response not ok:', response.status, response.statusText);
            console.log('❌ Error response body:', errorText);
          }
        } catch (serverError) {
          console.error('❌ Error loading preferences from server:', serverError);
          console.error('Error details:', serverError.message);
          console.error('Error stack:', serverError.stack);
        }
      }
    } catch (error) {
      console.error('Error loading lookingFor preference:', error);
    }
  };

  const checkLocationPermission = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      setLocationPermission(status);
    } catch (error) {
      console.error('Error checking location permission:', error);
      setLocationPermission('denied');
    }
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(status);

      if (status !== 'granted') {
        Alert.alert(
          "Location Permission Required",
          "Shake & Match needs location permission to find matches near you. Please enable location in your device settings.",
          [
            { text: "Cancel", style: "cancel" },
            {
              text: "Open Settings",
              onPress: () => {
                if (Platform.OS === 'ios') {
                  Linking.openURL('app-settings:');
                } else {
                  Linking.openSettings();
                }
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      Alert.alert('Error', 'Failed to request location permission.');
    }
  };

  // Load premium status
  const loadPremiumStatus = async () => {
    if (!user || !serverAddress) return;

    try {
      const token = await AsyncStorage.getItem('authToken');
      if (!token) return;

      const response = await fetch(`http://${serverAddress}/api/premium/status`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPremiumStatus(data);

        // Set current location display
        if (data.customLocation && data.customLocation.enabled) {
          setCurrentLocation({
            type: 'custom',
            display: `${data.customLocation.city}, ${data.customLocation.country}`,
            isPremium: true
          });
        } else {
          setCurrentLocation({
            type: 'gps',
            display: 'Your Location',
            isPremium: false
          });
        }
      }
    } catch (error) {
      console.error('Error loading premium status:', error);
    }
  };

  // Scale animation based on shake state
  const scale = shakeAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 1.2],
  });

  // Handle navigation to chat screen safely
  const handleChatNavigation = (matchItem) => {
    if (!matchItem || !matchItem.userId) {
      // Display error if match data is invalid
      Alert.alert(
        "Error",
        "Cannot open chat with this match. Try restarting the app.",
        [{ text: "OK" }]
      );
      return;
    }
    
    // Clear unread messages flag
    matchItem.hasUnreadMessages = false;
    
    // Navigate with valid match data
    navigation.navigate('Chat', { match: matchItem });
  };

  // Handle profile view navigation
  const handleViewProfile = (matchItem) => {
    if (!matchItem || !matchItem.userId) {
      Alert.alert(
        "Error",
        "Cannot view profile. Try restarting the app.",
        [{ text: "OK" }]
      );
      return;
    }
    
    navigation.navigate('ViewProfile', { 
      userId: matchItem.userId,
      username: matchItem.username,
      forceRefresh: true // Always force refresh from home screen
    });
  };

  // Handle manual shake button press - for testing
  const handleManualShake = () => {
    console.log("Manual shake button pressed");

    // Check location permission first
    if (locationPermission !== 'granted') {
      Alert.alert(
        "Location Required",
        "You need to enable location permission to use the shake feature. This helps us find matches near you.",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Enable Location",
            onPress: requestLocationPermission
          }
        ]
      );
      return;
    }

    const now = new Date();

    // Rate limit manual shakes to prevent spamming
    if (lastManualShake && (now - lastManualShake) < 2000) {
      console.log("Manual shake ignored - too soon");
      return;
    }

    setLastManualShake(now);
    setManualShakeCount(prev => prev + 1);

    // Use the same shake handler as accelerometer
    if (onShake) {
      onShake();
    }
  };

  // Render right actions for swipe
  const renderRightActions = (progress, matchId, matchUsername) => {
    const translateX = progress.interpolate({
      inputRange: [0, 1],
      outputRange: [160, 0],
    });

    return (
      <View style={homeStyles.swipeActionsContainer}>
        <Animated.View style={[homeStyles.actionContainer, { transform: [{ translateX }] }]}>
          <TouchableOpacity
            style={homeStyles.deleteAction}
            onPress={() => {
              if (onDeleteMatch) {
                onDeleteMatch(matchId);
              }
            }}
          >
            <Ionicons name="trash-outline" size={24} color="white" />
            <Text style={homeStyles.actionText}>Delete</Text>
          </TouchableOpacity>
        </Animated.View>

        <Animated.View style={[homeStyles.actionContainer, { transform: [{ translateX }] }]}>
          <TouchableOpacity
            style={homeStyles.unmatchAction}
            onPress={() => {
              Alert.alert(
                "Unmatch User",
                `Are you sure you want to unmatch with ${matchUsername}? You can match again in the future.`,
                [
                  { text: "Cancel", style: "cancel" },
                  {
                    text: "Unmatch",
                    style: "destructive",
                    onPress: () => {
                      if (onUnmatch) {
                        onUnmatch(matchId);
                      }
                    }
                  }
                ]
              );
            }}
          >
            <Ionicons name="person-remove-outline" size={24} color="white" />
            <Text style={homeStyles.actionText}>Unmatch</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={homeStyles.blockAction}
            onPress={() => {
              Alert.alert(
                "Block User",
                `Are you sure you want to block ${matchUsername}? You won't see each other in matches anymore.`,
                [
                  { text: "Cancel", style: "cancel" },
                  {
                    text: "Block",
                    style: "destructive",
                    onPress: () => {
                      if (onBlockUser) {
                        onBlockUser(matchId);
                      }
                    }
                  }
                ]
              );
            }}
          >
            <Ionicons name="ban-outline" size={24} color="white" />
            <Text style={homeStyles.actionText}>Block</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    );
  };

  // Format distance properly
  const formatDistance = (distance) => {
    // Check if distance exists and is a number
    if (distance !== undefined && distance !== null && !isNaN(parseFloat(distance))) {
      return `${Math.round(parseFloat(distance) * 10) / 10} km away`;
    }
    return 'Distance unknown';
  };

  // Format match creation time
  const formatMatchTime = (createdAt) => {
    if (!createdAt) return '';
    
    try {
      const created = new Date(createdAt);
      const now = new Date();
      const diffMs = now - created;
      
      // If match is less than 1 minute old, show "Just now"
      if (diffMs < 60000) {
        return 'Just now';
      }
      
      // Show time ago in minutes if less than 60 minutes
      if (diffMs < 3600000) {
        const minutes = Math.floor(diffMs / 60000);
        return `${minutes} min ago`;
      }
      
      // Show time ago in hours if less than 24 hours
      if (diffMs < 86400000) {
        const hours = Math.floor(diffMs / 3600000);
        return `${hours} hr ago`;
      }
      
      // Otherwise show the date
      return created.toLocaleDateString();
    } catch (e) {
      return '';
    }
  };

  // Render user profile icon - shows first image or default icon
  const renderProfileIcon = () => {
    // Use prop userProfile if available, otherwise use local state
    const currentUserProfile = propUserProfile || userProfile;
    const hasProfileImage = currentUserProfile && currentUserProfile.images && currentUserProfile.images.length > 0;

    if (hasProfileImage) {
      const imageUri = currentUserProfile.images[0].startsWith('data:')
        ? currentUserProfile.images[0]
        : `data:image/jpeg;base64,${currentUserProfile.images[0]}`;

      return (
        <View style={homeStyles.profileIconContainer}>
          <Image
            source={{ uri: imageUri }}
            style={homeStyles.profileIconImage}
            resizeMode="cover"
          />
        </View>
      );
    }

    // Fallback to default icon if no image available
    return <Ionicons name="person-circle-outline" size={28} color="#555" />;
  };

  // Render match avatar - shows profile image or letter fallback
  const renderMatchAvatar = (item) => {
    const profile = matchProfiles[item.userId];
    const hasProfileImage = profile && profile.images && profile.images.length > 0;

    if (hasProfileImage) {
      const imageUri = profile.images[0].startsWith('data:')
        ? profile.images[0]
        : `data:image/jpeg;base64,${profile.images[0]}`;

      return (
        <View style={homeStyles.avatarContainer}>
          <Image
            source={{ uri: imageUri }}
            style={homeStyles.avatarImage}
            resizeMode="cover"
          />
        </View>
      );
    }

    // Fallback to letter avatar if no image available
    return (
      <View style={homeStyles.avatarContainer}>
        <Text style={homeStyles.avatarText}>
          {item.username ? item.username.charAt(0).toUpperCase() : '?'}
        </Text>
      </View>
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: themeColors.primaryLight }}>
      <StatusBar barStyle="dark-content" backgroundColor={themeColors.primaryLight} />
      <SafeAreaView style={{ flex: 0, backgroundColor: themeColors.primaryLight }} />
      <LinearGradient
        colors={[themeColors.primaryLight, themeColors.primaryLight, themeColors.primaryLight]}
        style={{ flex: 1 }}
      >
        <GestureHandlerRootView style={homeStyles.container}>
        {/* Floating decorative shapes with smooth wobble animation */}
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape1,
            {
              transform: [
                { rotate: '25deg' },
                {
                  translateX: floatingAnimations[0].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-8, 0, 8]
                  })
                },
                {
                  translateY: floatingAnimations[0].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-6, 0, 6]
                  })
                }
              ]
            }
          ]}
        />
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape2,
            {
              transform: [
                { rotate: '45deg' },
                {
                  translateX: floatingAnimations[1].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [6, 0, -6]
                  })
                },
                {
                  translateY: floatingAnimations[1].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-5, 0, 5]
                  })
                }
              ]
            }
          ]}
        />
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape3,
            {
              transform: [
                { rotate: '15deg' },
                {
                  translateX: floatingAnimations[2].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-7, 0, 7]
                  })
                },
                {
                  translateY: floatingAnimations[2].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [8, 0, -8]
                  })
                }
              ]
            }
          ]}
        />
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape4,
            {
              transform: [
                { rotate: '60deg' },
                {
                  translateX: floatingAnimations[3].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [5, 0, -5]
                  })
                },
                {
                  translateY: floatingAnimations[3].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-7, 0, 7]
                  })
                }
              ]
            }
          ]}
        />
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape5,
            {
              transform: [
                { rotate: '30deg' },
                {
                  translateX: floatingAnimations[4].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-9, 0, 9]
                  })
                },
                {
                  translateY: floatingAnimations[4].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [6, 0, -6]
                  })
                }
              ]
            }
          ]}
        />
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape6,
            {
              transform: [
                { rotate: '75deg' },
                {
                  translateX: floatingAnimations[5].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [6, 0, -6]
                  })
                },
                {
                  translateY: floatingAnimations[5].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-5, 0, 5]
                  })
                }
              ]
            }
          ]}
        />
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape7,
            {
              transform: [
                { rotate: '20deg' },
                {
                  translateX: floatingAnimations[6].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-6, 0, 6]
                  })
                },
                {
                  translateY: floatingAnimations[6].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [4, 0, -4]
                  })
                }
              ]
            }
          ]}
        />
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape8,
            {
              transform: [
                { rotate: '50deg' },
                {
                  translateX: floatingAnimations[7].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [8, 0, -8]
                  })
                },
                {
                  translateY: floatingAnimations[7].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-5, 0, 5]
                  })
                }
              ]
            }
          ]}
        />
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape9,
            {
              transform: [
                { rotate: '35deg' },
                {
                  translateX: floatingAnimations[8].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-5, 0, 5]
                  })
                },
                {
                  translateY: floatingAnimations[8].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [7, 0, -7]
                  })
                }
              ]
            }
          ]}
        />
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape10,
            {
              transform: [
                { rotate: '65deg' },
                {
                  translateX: floatingAnimations[10].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [6, 0, -6]
                  })
                },
                {
                  translateY: floatingAnimations[10].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-8, 0, 8]
                  })
                }
              ]
            }
          ]}
        />
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape11,
            {
              transform: [
                { rotate: '40deg' },
                {
                  translateX: floatingAnimations[11].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-7, 0, 7]
                  })
                },
                {
                  translateY: floatingAnimations[11].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [4, 0, -4]
                  })
                }
              ]
            }
          ]}
        />
        <Animated.View
          style={[
            homeStyles.floatingShape,
            homeStyles.floatingShape12,
            {
              transform: [
                { rotate: '55deg' },
                {
                  translateX: floatingAnimations[12].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [5, 0, -5]
                  })
                },
                {
                  translateY: floatingAnimations[12].interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [-6, 0, 6]
                  })
                }
              ]
            }
          ]}
        />
        
        {/* Header */}
        <View style={homeStyles.header}>
          <TouchableOpacity
            style={homeStyles.headerButtonLeft}
            onPress={() => navigation.navigate('ProfileScreen')}
          >
            {renderProfileIcon()}
          </TouchableOpacity>
          
          <Text style={[homeStyles.headerTitle, { color: themeColors.textPrimary }]}>Shake & Match</Text>
          
          <TouchableOpacity
            style={homeStyles.headerButtonRight}
            onPress={() => navigation.navigate('Settings')}
          >
            <Ionicons name="settings-outline" size={28} color={themeColors.textSecondary} />
          </TouchableOpacity>
        </View>
        
        {/* Welcome section */}
        <View style={homeStyles.profileSection}>
          <Text style={[homeStyles.welcomeText, { color: themeColors.textPrimary }]}>Hi, {user.username}!</Text>
          <Text style={[homeStyles.instructionText, { color: themeColors.textSecondary }]}>
            {lookingFor === 'friends'
              ? 'Shake your phone to match your BFF! ♡'
              : 'Shake your phone to match your soul! ♡'
            }
          </Text>

          {/* Location indicator */}
          {currentLocation && (
            <View style={homeStyles.locationIndicator}>
              <Ionicons
                name={currentLocation.type === 'custom' ? 'location' : 'location-outline'}
                size={16}
                color={currentLocation.isPremium ? '#FFD700' : '#666'}
              />
              <Text style={[
                homeStyles.locationText,
                currentLocation.isPremium && homeStyles.premiumLocationText
              ]}>
                {currentLocation.display}
              </Text>
              {currentLocation.isPremium && (
                <View style={homeStyles.premiumLocationBadge}>
                  <Ionicons name="star" size={12} color="#FFD700" />
                  <Text style={homeStyles.premiumLocationBadgeText}>PREMIUM</Text>
                </View>
              )}
            </View>
          )}
        </View>
        
        {/* Shake section - clean and simple */}
        <View style={homeStyles.shakeSection}>
          <Animated.View
            style={[
              homeStyles.shakeOuterCircle,
              { transform: [{ scale }] }
            ]}
          >
            <TouchableOpacity
              style={[
                homeStyles.shakeButton,
                dynamicStyles.shakeButton,
                locationPermission !== 'granted' && homeStyles.shakeButtonDisabled
              ]}
              onPress={handleManualShake}
              activeOpacity={0.7}
            >
              <Ionicons
                name={locationPermission === 'granted' ? "phone-portrait" : "location-outline"}
                size={50}
                color="#fff"
              />
              <Text style={homeStyles.shakeText}>
                {locationPermission === 'granted' ? 'SHAKE' : 'ENABLE LOCATION'}
              </Text>
            </TouchableOpacity>
          </Animated.View>

          {/* Fixed height container for the bubble to prevent layout shifts */}
          <View style={homeStyles.bubbleContainer}>
            {shakeDetected && (
              <View style={homeStyles.searchingBubble}>
                <Text style={homeStyles.searchingText}>
                  Searching for nearby matches...
                </Text>
              </View>
            )}
          </View>
        </View>
        
        {/* Matches card container */}
        <View style={[homeStyles.matchesContainer, { backgroundColor: `${themeColors.bgPrimary}60` }]}>
          <Text style={[homeStyles.sectionTitle, { color: themeColors.textPrimary }]}>Your Matches</Text>
          
          {matches.length === 0 ? (
            <View style={homeStyles.emptyMatchesCard}>
              <Ionicons name="people-outline" size={48} color="#ccc" />
              <Text style={homeStyles.noMatchesText}>
                No matches yet.
              </Text>
            </View>
          ) : (
            <FlatList
              data={matches}
              keyExtractor={(item) => item.userId || Math.random().toString()}
              renderItem={({ item }) => (
                <Swipeable
                  renderRightActions={(progress) => 
                    renderRightActions(progress, item.userId, item.username)
                  }
                  overshootRight={false}
                >
                  <View style={homeStyles.matchItem}>
                    <TouchableOpacity
                      onPress={() => handleViewProfile(item)}
                    >
                      {renderMatchAvatar(item)}
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                      style={homeStyles.matchInfo}
                      onPress={() => handleViewProfile(item)}
                    >
                      <View style={homeStyles.matchNameRow}>
                        <Text style={homeStyles.matchName}>{item.username || 'Unknown User'}</Text>
                        {(item.verification?.isVerified || matchProfiles[item.userId]?.verification?.isVerified) && (
                          <View style={[
                            homeStyles.verificationBadgeSmall,
                            (item.badgeType === 'gold' || matchProfiles[item.userId]?.badgeType === 'gold') ? homeStyles.goldBadgeSmall : homeStyles.blueBadgeSmall
                          ]}>
                            <Ionicons
                              name="checkmark-circle"
                              size={12}
                              color={(item.badgeType === 'gold' || matchProfiles[item.userId]?.badgeType === 'gold') ? '#FFD700' : themeColors.primary}
                            />
                            <Text style={[
                              homeStyles.verificationTextSmall,
                              (item.badgeType === 'gold' || matchProfiles[item.userId]?.badgeType === 'gold') ? homeStyles.goldTextSmall : homeStyles.blueTextSmall
                            ]}>
                              Verified
                            </Text>
                          </View>
                        )}
                        {item.matchType === 'friends' && (
                          <View style={homeStyles.friendBadge}>
                            <Ionicons name="people" size={12} color="#e83333" />
                            <Text style={homeStyles.friendBadgeText}>Friend</Text>
                          </View>
                        )}
                      </View>
                      <View style={homeStyles.matchDetailsRow}>
                        <Text style={homeStyles.matchDistance}>
                          {formatDistance(item.distance)}
                        </Text>
                        {item.createdAt && !item.hasActivity && (
                          <View style={homeStyles.matchTimestampContainer}>
                            <Text style={homeStyles.matchTimestamp}>
                              {formatMatchTime(item.createdAt)}
                            </Text>
                            {matchTimers[item.userId] && (
                              <Text style={homeStyles.expiryWarning}>
                                • {matchTimers[item.userId]}
                              </Text>
                            )}
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                    
                    {item.hasUnreadMessages && (
                      <View style={homeStyles.unreadBadge}>
                        <Text style={homeStyles.unreadBadgeText}>New</Text>
                      </View>
                    )}
                    
                    <TouchableOpacity 
                      style={homeStyles.chatButton}
                      onPress={() => handleChatNavigation(item)}
                    >
                      <Ionicons name="chatbubble-outline" size={24} color={themeColors.primary} />
                    </TouchableOpacity>
                  </View>
                </Swipeable>
              )}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={homeStyles.matchesListContent}
            />
          )}
        </View>
        </GestureHandlerRootView>
      </LinearGradient>
    </View>
  );
};

// Styles are now imported from ScreensStyles.js


export default HomeScreen;
