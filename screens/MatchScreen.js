// Update MatchScreen.js to fetch and display profile information
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Image, ScrollView, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { matchStyles, theme } from './ScreensStyles';
import { useThemedStyles, useThemeColors } from '../contexts/ThemeContext';

const MatchScreen = ({ route, navigation, serverAddress }) => {
  // Force re-render when theme changes
  useThemedStyles();
  // Get current theme colors
  const themeColors = useThemeColors();

  const { match } = route.params;
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const [matchProfile, setMatchProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
    
    // Fetch match profile data
    fetchMatchProfile();
  }, []);
  
  const fetchMatchProfile = async () => {
    try {
      // First check if we have cached profile data
      const cachedProfile = await AsyncStorage.getItem(`profile_${match.userId}`);
      
      if (cachedProfile) {
        setMatchProfile(JSON.parse(cachedProfile));
      }
      
      // Try to fetch fresh data from server
      if (serverAddress) {
        const response = await fetch(`http://${serverAddress}/api/profile/${match.userId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (response.ok) {
          const data = await response.json();
          setMatchProfile(data.profile);
          
          // Cache the profile data
          await AsyncStorage.setItem(`profile_${match.userId}`, JSON.stringify(data.profile));
        }
      }
    } catch (error) {
      console.error('Error fetching match profile:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle view full profile button press
  const handleViewFullProfile = () => {
    navigation.navigate('ViewProfile', {
      userId: match.userId,
      username: match.username,
      forceRefresh: true // Always force refresh from match screen
    });
  };

  return (
    <ScrollView style={matchStyles.matchContainer}>
      <Animated.View 
        style={[matchStyles.matchContent, { opacity: fadeAnim }]}
      >
        <Text style={matchStyles.matchTitle}>It's a Match!</Text>
        <Text style={matchStyles.matchSubtitle}>
          You and {match.username} shook your phones at the same time!
        </Text>
        
        {loading ? (
          <ActivityIndicator size="large" color="#e83333" style={matchStyles.loader} />
        ) : (
          <>
            {/* Profile Image Section */}
            <TouchableOpacity 
              style={matchStyles.avatarContainer}
              onPress={handleViewFullProfile}
            >
              {matchProfile && matchProfile.images && matchProfile.images.length > 0 ? (
                <Image 
                  source={{ 
                    uri: matchProfile.images[0].startsWith('data:') 
                      ? matchProfile.images[0] 
                      : `data:image/jpeg;base64,${matchProfile.images[0]}` 
                  }} 
                  style={matchStyles.profileImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={matchStyles.largeAvatar}>
                  <Text style={matchStyles.largeAvatarText}>
                    {match.username.charAt(0).toUpperCase()}
                  </Text>
                </View>
              )}
              <View style={matchStyles.viewProfileIndicator}>
                <Ionicons name="eye" size={20} color="#fff" />
              </View>
            </TouchableOpacity>
            
            {/* User Info Section */}
            <View style={matchStyles.userInfoContainer}>
              <View style={matchStyles.nameRow}>
                <Text style={matchStyles.userName}>
                  {match.username}
                  {matchProfile?.age ? `, ${matchProfile.age}` : ''}
                </Text>
                {matchProfile?.verification?.isVerified && (
                  <View style={[
                    matchStyles.verificationBadge,
                    matchProfile.badgeType === 'gold' ? matchStyles.goldBadge : matchStyles.blueBadge
                  ]}>
                    <Ionicons
                      name="checkmark-circle"
                      size={16}
                      color={matchProfile.badgeType === 'gold' ? '#FFD700' : themeColors.primary}
                    />
                    <Text style={[
                      matchStyles.verificationText,
                      matchProfile.badgeType === 'gold' ? matchStyles.goldText : matchStyles.blueText
                    ]}>
                      Verified
                    </Text>
                  </View>
                )}
              </View>

              <Text style={matchStyles.distanceText}>
                {Math.round(match.distance * 10) / 10} kilometers away
              </Text>
              
              {matchProfile?.description && (
                <Text style={matchStyles.descriptionText}>
                  {matchProfile.description}
                </Text>
              )}
              
              {matchProfile?.passions && matchProfile.passions.length > 0 && (
                <View style={matchStyles.passionsContainer}>
                  {matchProfile.passions.map((passion, index) => (
                    <View key={index} style={matchStyles.passionTag}>
                      <Text style={matchStyles.passionText}>{passion}</Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          </>
        )}
        
        <TouchableOpacity
          style={matchStyles.chatButton}
          onPress={() => navigation.navigate('Chat', { match })}
        >
          <Text style={matchStyles.chatButtonText}>Send a Message</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={matchStyles.viewProfileButton}
          onPress={handleViewFullProfile}
        >
          <Text style={matchStyles.viewProfileText}>View Full Profile</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={matchStyles.laterButton}
          onPress={() => navigation.navigate('Home')}
        >
          <Text style={matchStyles.laterButtonText}>Later</Text>
        </TouchableOpacity>
      </Animated.View>
    </ScrollView>
  );
};

// Styles are now imported from ScreensStyles.js

export default MatchScreen;

